import { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, Text, ScrollView, RefreshControl, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  Shield, Bell, FileText, ClipboardList, FileCheck, AlertTriangle,
  Wallet, X
} from 'lucide-react-native';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import PolicyCard from '@/components/dashboard/PolicyCard';
import ActionButton from '@/components/ui/ActionButton';
import Button from '@/components/ui/Button';
import PromotionalBanner from '@/components/dashboard/PromotionalBanner';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchProfile, selectIsProfileComplete } from '@/store/profileSlice';
import { router } from 'expo-router';
import WavingHand from '@/components/dashboard/WavingHand';
import { showToast } from '@/utils/toast';
import useApplicationStore from '@/store/applicationStore';

// Initialize with empty array - will be populated with user data
const activePolicies: any[] = [];

// Define quick actions inside the component to access theme colors
const getQuickActions = (colors: any) => [
  { id: '1', title: 'Claims', icon: ClipboardList, color: colors.primary[500], route: '/claims' },
  { id: '2', title: 'Quotes', icon: FileText, color: colors.secondary[500], route: '/quotes' },
  { id: '3', title: 'Manage Documents ', icon: FileCheck, color: colors.success[500], route: '/documents' },
  { id: '4', title: 'Payments', icon: Wallet, color: colors.primary[500], route: '/payment' },
  { id: '5', title: 'Test Navigation', icon: FileText, color: colors.warning[500], route: '/documents/test' },
];

// Initialize with empty array - will be populated with user data
const notifications: any[] = [];

// Get user from Redux store - moved inside component
// const user = useAppSelector(state => state.auth.user);

// Define styles with a function to access theme values
const getStyles = (spacing: any, typography: any, borders: any, shadows: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: spacing['3xl'],
  },
  welcomeSection: {
    padding: spacing.lg,
    marginHorizontal: spacing.lg,
    marginTop: spacing.md,
    borderRadius: borders.radius.lg,
  },
  welcomeText: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes['2xl'],
    marginBottom: spacing.xs,
    letterSpacing: -0.5, // Tighter letter spacing like Glassdoor
  },
  welcomeSubtext: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.md,
    opacity: 0.8,
    letterSpacing: -0.2, // Slightly tighter letter spacing like Glassdoor
  },
  welcomeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  waveEmoji: {
    fontSize: typography.sizes['2xl'],
    marginLeft: spacing.sm,
  },
  sectionTitle: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.lg,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    marginTop: spacing.lg,
    letterSpacing: -0.3, // Tighter letter spacing like Glassdoor
  },
  quickActionsContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.md,
    gap: spacing.xl,
  },
  quickActionsRowCentered: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xl * 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    marginTop: spacing.lg,
  },
  viewAllText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
    letterSpacing: -0.2, // Slightly tighter letter spacing like Glassdoor
    textDecorationLine: 'none', // Glassdoor doesn't use underlines for links
  },
  policiesContainer: {
    marginHorizontal: spacing.lg,
  },
  notificationsContainer: {
    marginHorizontal: spacing.lg,
  },
  notificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borders.radius.lg,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  notificationContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  notificationTitle: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.md,
    marginBottom: 2,
    letterSpacing: -0.3, // Tighter letter spacing like Glassdoor
  },
  notificationMessage: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.sm,
    letterSpacing: -0.2, // Slightly tighter letter spacing like Glassdoor
  },
  notificationTime: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.xs,
    marginLeft: spacing.sm,
    letterSpacing: -0.1, // Slightly tighter letter spacing like Glassdoor
  },
});

// Check if profile is complete
const isProfileComplete = (user: any) => {
  if (!user) return false;

  // Common required fields
  const commonFieldsComplete = !!(
    user.email &&
    user.phone &&
    user.address
  );

  // Check user type specific fields
  if (user.userType === 'individual') {
    return !!(
      commonFieldsComplete &&
      user.firstName &&
      user.lastName
    );
  } else if (user.userType === 'business') {
    return !!(
      commonFieldsComplete &&
      user.companyName &&
      user.registrationNumber &&
      user.contactPersonName
    );
  }

  // Default to individual check if userType is not specified
  return !!(
    commonFieldsComplete &&
    user.firstName &&
    user.lastName
  );
};

export default function HomeScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const [showProfileBanner, setShowProfileBanner] = useState(true);
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders, shadows } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  // Get user from Redux store
  const user = useAppSelector(state => state.auth.user);
  const isProfileCompleteFromStore = useAppSelector(selectIsProfileComplete);

  // Get applications from store
  const { applications, fetchApplications } = useApplicationStore();

  // Fetch profile and applications when component mounts
  useEffect(() => {
    setShowProfileBanner(true);
    dispatch(fetchProfile());
    fetchApplications();
  }, [dispatch, fetchApplications]);

  // Create styles with the current theme
  const styles = getStyles(spacing, typography, borders, shadows);

  // Additional styles that need access to colors
  const additionalStyles = StyleSheet.create({
    profileBanner: {
      position: 'relative',
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: isDarkMode ? colors.warning[900] + '40' : colors.warning[100],
      marginHorizontal: spacing.lg,
      marginTop: spacing.md,
      padding: spacing.md,
      borderRadius: borders.radius.lg,
      borderLeftWidth: 4,
      borderLeftColor: colors.warning[500],
    },
    closeButton: {
      position: 'absolute',
      top: spacing.xs,
      right: spacing.xs,
      padding: spacing.xs,
      zIndex: 1
    },
    profileBannerContent: {
      flex: 1,
      marginLeft: spacing.sm,
      marginRight: spacing.md,
    },
    profileBannerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: isDarkMode ? colors.white : colors.text,
    },
    profileBannerText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: isDarkMode ? colors.textSecondary : colors.neutral[600],
      marginTop: 2,
    },
    profileBannerButton: {
      backgroundColor: colors.warning[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
      marginTop: -spacing.xs, // Push the button up a bit
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    profileBannerButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
      textAlign: 'center',
    },
  });

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const renderQuickActions = () => {
    const quickActions = getQuickActions(colors);
    const topRowActions = quickActions.slice(0, 3);
    const bottomRowActions = quickActions.slice(3, 5);

    return (
      <View style={styles.quickActionsContainer}>
        {/* Top row with 3 items */}
        <View style={styles.quickActionsRow}>
          {topRowActions.map((action, index) => (
            <Animated.View
              key={action.id}
              entering={FadeInDown.delay(300 + index * 100).springify()}
            >
              <ActionButton
                title={action.title}
                icon={action.icon}
                color={action.color}
                onPress={() => handleQuickActionPress(action)}
              />
            </Animated.View>
          ))}
        </View>

        {/* Bottom row with 2 items */}
        <View style={styles.quickActionsRowCentered}>
          {bottomRowActions.map((action, index) => (
            <Animated.View
              key={action.id}
              entering={FadeInDown.delay(600 + index * 100).springify()}
            >
              <ActionButton
                title={action.title}
                icon={action.icon}
                color={action.color}
                onPress={() => handleQuickActionPress(action)}
              />
            </Animated.View>
          ))}
        </View>
      </View>
    );
  };

  // Handle quick action press
  const handleQuickActionPress = (action: any) => {
    try {
      // Special handling for payments - check if user has applications
      if (action.route === '/payment') {
        // Check if user has any applications that need payment
        const applicationsWithPayment = applications.filter(app =>
          app.status === 'payment_pending' ||
          (app.payment && app.payment.status !== 'verified')
        );

        if (applicationsWithPayment.length > 0) {
          // Navigate to payment screen with the first application that needs payment
          router.push({
            pathname: '/payment',
            params: { applicationId: applicationsWithPayment[0].id }
          });
        } else if (applications.length > 0) {
          // If no applications need payment but user has applications, show payment history
          router.push('/payment/history');
        } else {
          // No applications yet
          showToast(
            'info',
            'No Applications',
            'You need to submit an application first before making payments.',
            { visibilityTime: 4000 }
          );
        }
      } else {
        router.push(action.route);
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const renderNotifications = () => (
    <View style={styles.notificationsContainer}>
      {notifications.length > 0 ? (
        notifications.map((notification, index) => (
          <Animated.View
            key={notification.id}
            entering={FadeInDown.delay(800 + index * 100).springify()}
            style={[styles.notificationCard, { backgroundColor: colors.card }]}
          >
            <Bell size={20} color={colors.primary[500]} />
            <View style={styles.notificationContent}>
              <Text style={[styles.notificationTitle, { color: colors.text }]}>{notification.title}</Text>
              <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>{notification.message}</Text>
            </View>
            <Text style={[styles.notificationTime, { color: colors.textSecondary }]}>{notification.time}</Text>
          </Animated.View>
        ))
      ) : (
        <Animated.View
          entering={FadeInDown.delay(800).springify()}
          style={[styles.notificationCard, { backgroundColor: colors.card }]}
        >
          <Bell size={20} color={colors.primary[500]} />
          <View style={styles.notificationContent}>
            <Text style={[styles.notificationTitle, { color: colors.text }]}>No Notifications</Text>
            <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>You don't have any notifications yet</Text>
          </View>
        </Animated.View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <DashboardHeader
        title="INERCA"
        notificationCount={2}
        onNotificationPress={() => console.log('Notifications pressed')}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Profile Completion Banner */}
        {!isProfileCompleteFromStore && showProfileBanner && (
          <Animated.View
            style={additionalStyles.profileBanner}
            entering={FadeInDown.delay(50).springify()}
          >
            <AlertTriangle size={20} color={colors.warning[500]} />
            <View style={additionalStyles.profileBannerContent}>
              <Text style={additionalStyles.profileBannerTitle}>Complete Your Profile</Text>
              <Text style={additionalStyles.profileBannerText}>
                Please complete your profile to get the most out of our services
              </Text>
            </View>
            <Button
              title="Complete"
              variant="primary"
              size="small"
              onPress={() => router.push('/(app)/profile/edit')}
              style={{ backgroundColor: colors.warning[500] }}
            />
            <TouchableOpacity
              style={additionalStyles.closeButton}
              onPress={() => setShowProfileBanner(false)}
            >
              <X size={18} color={colors.warning[500]} />
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Welcome Section */}
        <Animated.View
          style={[styles.welcomeSection, { backgroundColor: colors.transparent }]}
          entering={FadeInDown.delay(100).springify()}
        >
          <View style={styles.welcomeRow}>
            <Text style={[styles.welcomeText, { color: colors.text }]}>
              Welcome back, {user?.userType === 'business' ? (user?.companyName || 'Company') : (user?.firstName || 'Guest')}
            </Text>
            <WavingHand size={28} style={styles.waveEmoji} />
          </View>
          <Text style={[styles.welcomeSubtext, { color: colors.textSecondary }]}>
            Your insurance dashboard
          </Text>
        </Animated.View>

        {/* Promotional Banners */}
        <Animated.View entering={FadeInDown.delay(200).springify()}>
          <PromotionalBanner />
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View entering={FadeInDown.delay(300).springify()}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Quick Actions</Text>
          {renderQuickActions()}
        </Animated.View>

        {/* Active Policies */}
        <Animated.View entering={FadeInDown.delay(400).springify()}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Your Policies</Text>
            <TouchableOpacity onPress={() => console.log('View all policies')}>
              <Text style={[styles.viewAllText, { color: colors.primary[500] }]}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.policiesContainer}>
            {activePolicies.length > 0 ? (
              activePolicies.map((policy, index) => (
                <Animated.View
                  key={policy.id}
                  entering={FadeInDown.delay(500 + index * 100).springify()}
                >
                  <PolicyCard policy={policy} />
                </Animated.View>
              ))
            ) : (
              <Animated.View
                entering={FadeInDown.delay(500).springify()}
                style={[styles.notificationCard, { backgroundColor: colors.card }]}
              >
                <Shield size={20} color={colors.primary[500]} />
                <View style={styles.notificationContent}>
                  <Text style={[styles.notificationTitle, { color: colors.text }]}>No Active Policies</Text>
                  <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>
                    You don't have any active policies yet. Get a quote to start.
                  </Text>
                </View>
              </Animated.View>
            )}
          </View>
        </Animated.View>

        {/* Notifications */}
        <Animated.View entering={FadeInDown.delay(700).springify()}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Notifications</Text>
            <TouchableOpacity onPress={() => console.log('View all notifications')}>
              <Text style={[styles.viewAllText, { color: colors.primary[500] }]}>View All</Text>
            </TouchableOpacity>
          </View>
          {renderNotifications()}
        </Animated.View>
      </ScrollView>

      {/* Bottom Navigation Bar */}
      {/* <BottomNavBar currentRoute="home" /> */}
    </SafeAreaView>
  );
}



