import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Share,
  Linking
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  XCircle,
  RefreshCw,
  Share2,
  Upload,
  AlertCircle
} from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import usePaymentStore from '@/store/paymentStore';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';
import { format, parseISO } from 'date-fns';
import { formatCurrency } from '@/utils/quoteCalculations';
import { PaymentHistoryItem, PaymentHistoryStatus } from '@/types/payment.types';
// Payment service removed - using document upload context instead

// Mock payment data for demonstration
const mockPayments = [
  {
    id: '1',
    reference: 'PAY-123456',
    amount: 1500,
    currency: 'BWP',
    date: '2023-05-15',
    status: 'completed',
    method: 'eft',
    description: 'Premium payment for Motor Insurance',
    policyNumber: 'POL-12345',
    receiptUrl: 'https://example.com/receipts/1.pdf',
    proofOfPaymentUrl: 'https://example.com/proofs/1.pdf',
    verificationDate: '2023-05-16'
  },
  {
    id: '2',
    reference: 'PAY-234567',
    amount: 2500,
    currency: 'BWP',
    date: '2023-06-10',
    status: 'pending',
    method: 'eft',
    description: 'Premium payment for Home Insurance',
    policyNumber: 'POL-23456'
  },
  {
    id: '3',
    reference: 'PAY-345678',
    amount: 750,
    currency: 'BWP',
    date: '2023-07-05',
    dueDate: '2023-08-05',
    status: 'processing',
    method: 'direct_debit',
    description: 'Premium payment for Life Insurance',
    policyNumber: 'POL-34567'
  },
  {
    id: '4',
    reference: 'PAY-456789',
    amount: 3000,
    currency: 'BWP',
    date: '2023-08-01',
    status: 'failed',
    method: 'eft',
    description: 'Premium payment for Business Insurance',
    policyNumber: 'POL-45678'
  },
  {
    id: '5',
    reference: 'PAY-567890',
    amount: 1200,
    currency: 'BWP',
    date: '2023-09-15',
    dueDate: '2023-10-15',
    status: 'completed',
    method: 'direct_debit',
    description: 'Premium payment for Travel Insurance',
    policyNumber: 'POL-56789',
    receiptUrl: 'https://example.com/receipts/5.pdf',
    proofOfPaymentUrl: 'https://example.com/proofs/5.pdf',
    verificationDate: '2023-09-16'
  }
];

export default function PaymentDetailScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const paymentId = params.id as string;

  // Get payment store methods - simplified for document upload only
  const {
    getPaymentById,
    fetchPaymentHistory
  } = usePaymentStore();

  // State
  const [payment, setPayment] = useState<PaymentHistoryItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load payment details
  useEffect(() => {
    const loadPayment = async () => {
      setIsLoading(true);
      try {
        // First try to get from store
        await fetchPaymentHistory();
        const storedPayment = getPaymentById(paymentId);

        if (storedPayment) {
          setPayment(storedPayment);
        } else {
          // Fallback to mock data for demo
          const foundPayment = mockPayments.find(p => p.id === paymentId);

          if (foundPayment) {
            // Convert mock payment to PaymentHistoryItem
            setPayment({
              id: foundPayment.id,
              reference: foundPayment.reference,
              amount: foundPayment.amount,
              currency: foundPayment.currency,
              date: foundPayment.date,
              dueDate: foundPayment.dueDate,
              status: foundPayment.status as PaymentHistoryStatus,
              method: foundPayment.method as any,
              description: foundPayment.description,
              type: 'premium',
              category: 'policy',
              policyId: foundPayment.policyNumber ? `policy-${foundPayment.policyNumber}` : undefined,
              policyNumber: foundPayment.policyNumber,
              receiptId: foundPayment.receiptUrl ? `receipt-${foundPayment.id}` : undefined,
              proofOfPaymentId: foundPayment.proofOfPaymentUrl ? `proof-${foundPayment.id}` : undefined,
              verificationDate: foundPayment.verificationDate
            });
          } else {
            showToast(
              'error',
              'Payment Not Found',
              'The requested payment could not be found',
              { visibilityTime: 3000 }
            );
            router.back();
          }
        }
      } catch (error) {
        console.error('Error loading payment:', error);
        showToast(
          'error',
          'Error',
          'Failed to load payment details. Please try again.',
          { visibilityTime: 3000 }
        );
        router.back();
      } finally {
        setIsLoading(false);
      }
    };

    loadPayment();
  }, [paymentId]);

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    try {
      return format(parseISO(dateString), 'dd MMM yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'processing':
        return colors.info[500];
      case 'completed':
        return colors.success[500];
      case 'failed':
        return colors.error[500];
      case 'refunded':
        return colors.warning[700];
      case 'cancelled':
        return colors.textSecondary;
      default:
        return colors.textSecondary;
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    const color = getStatusColor(status);

    switch (status) {
      case 'pending':
        return <Clock size={24} color={color} />;
      case 'processing':
        return <RefreshCw size={24} color={color} />;
      case 'completed':
        return <CheckCircle size={24} color={color} />;
      case 'failed':
        return <XCircle size={24} color={color} />;
      case 'refunded':
        return <RefreshCw size={24} color={color} />;
      case 'cancelled':
        return <XCircle size={24} color={color} />;
      default:
        return <Clock size={24} color={color} />;
    }
  };

  // Get status display name
  const getStatusDisplayName = (status: string): string => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Handle upload proof
  const handleUploadProof = () => {
    router.push({
      pathname: '/payments/verify',
      params: { id: paymentId }
    });
  };

  // Simplified - no receipt generation, only document upload for proof of payment

  // Share payment details
  const sharePaymentDetails = async () => {
    if (!payment) return;

    try {
      const message = `
Payment Details:
Reference: ${payment.reference}
Amount: ${formatCurrency(payment.amount, payment.currency)}
Description: ${payment.description}
Date: ${formatDate(payment.date)}
Status: ${getStatusDisplayName(payment.status)}
Method: ${payment.method === 'eft' ? 'Electronic Funds Transfer (EFT)' : 'Direct Debit'}
${payment.policyNumber ? `Policy: ${payment.policyNumber}` : ''}
${payment.dueDate ? `Due Date: ${formatDate(payment.dueDate)}` : ''}
`;

      await Share.share({
        message,
        title: 'Payment Details'
      });
    } catch (error) {
      console.error('Error sharing payment details:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Details</Text>
      </View>

      <View style={styles.content}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
          </View>
        ) : payment ? (
          <ScrollView style={styles.scrollContainer}>
            {/* Payment Status */}
            <View style={styles.statusContainer}>
              {getStatusIcon(payment.status)}
              <Text style={[styles.statusText, { color: getStatusColor(payment.status) }]}>
                {getStatusDisplayName(payment.status)}
              </Text>
            </View>

            {/* Payment Details */}
            <View style={styles.detailsCard}>
              <Text style={styles.cardTitle}>Payment Information</Text>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Amount</Text>
                <Text style={styles.detailValue}>
                  {formatCurrency(payment.amount, payment.currency)}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Reference</Text>
                <Text style={styles.detailValue}>{payment.reference}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Description</Text>
                <Text style={styles.detailValue}>{payment.description}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date</Text>
                <Text style={styles.detailValue}>{formatDate(payment.date)}</Text>
              </View>

              {payment.dueDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Due Date</Text>
                  <Text style={styles.detailValue}>{formatDate(payment.dueDate)}</Text>
                </View>
              )}

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Method</Text>
                <Text style={styles.detailValue}>
                  {payment.method === 'eft' ? 'Electronic Funds Transfer (EFT)' : 'Direct Debit'}
                </Text>
              </View>

              {payment.policyNumber && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Policy</Text>
                  <Text style={styles.detailValue}>{payment.policyNumber}</Text>
                </View>
              )}

              {payment.verificationDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Verified On</Text>
                  <Text style={styles.detailValue}>{formatDate(payment.verificationDate)}</Text>
                </View>
              )}
            </View>

            {/* Actions - Simplified for document upload only */}
            <View style={styles.actionsContainer}>
              {payment.status === 'pending' && !payment.proofOfPaymentId && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.primaryButton]}
                  onPress={handleUploadProof}
                >
                  <Upload size={20} color={colors.white} />
                  <Text style={styles.primaryButtonText}>Upload Proof of Payment</Text>
                </TouchableOpacity>
              )}

              {payment.status === 'completed' && payment.proofOfPaymentId && (
                <View style={styles.successMessageContainer}>
                  <CheckCircle size={24} color={colors.success[500]} />
                  <Text style={styles.successMessageText}>
                    Payment verified successfully. Your proof of payment has been processed.
                  </Text>
                </View>
              )}

              {payment.status === 'failed' && (
                <View style={styles.errorMessageContainer}>
                  <AlertCircle size={24} color={colors.error[500]} />
                  <Text style={styles.errorMessageText}>
                    This payment failed to process. Please contact support for assistance.
                  </Text>
                </View>
              )}

              <TouchableOpacity
                style={[styles.actionButton, styles.outlineButton]}
                onPress={sharePaymentDetails}
              >
                <Share2 size={20} color={colors.primary[500]} />
                <Text style={styles.outlineButtonText}>Share Payment Details</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        ) : (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Payment not found</Text>
          </View>
        )}
      </View>

      <BottomNavBar currentRoute="payments" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#f44336',
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
    marginBottom: 16,
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  detailsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    maxWidth: '60%',
    textAlign: 'right',
  },
  actionsContainer: {
    padding: 16,
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#6200ee',
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  secondaryActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    flex: 1,
    marginHorizontal: 4,
  },
  secondaryButtonText: {
    color: '#6200ee',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#6200ee',
  },
  outlineButtonText: {
    color: '#6200ee',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  errorMessageContainer: {
    backgroundColor: '#ffebee',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorMessageText: {
    color: '#d32f2f',
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  successMessageContainer: {
    backgroundColor: '#e8f5e8',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  successMessageText: {
    color: '#2e7d32',
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
});
