import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Platform, TextInput, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  Shield, Search, Filter, AlertCircle, Check, Clock, Calendar,
  X, SortAsc, SortDesc, ChevronDown, DollarSign, Tag, Calendar as CalendarIcon
} from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import usePolicyStore, { Policy, PolicyStatus } from '@/store/policyStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function PoliciesScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const { policies, fetchPolicies, isLoading } = usePolicyStore();
  const [filterStatus, setFilterStatus] = useState<PolicyStatus | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [sortBy, setSortBy] = useState<'date' | 'premium' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterType, setFilterType] = useState<string | null>(null);
  const [premiumRange, setPremiumRange] = useState<[number | null, number | null]>([null, null]);

  useEffect(() => {
    fetchPolicies();
  }, [fetchPolicies]);

  // Toggle sort order
  const toggleSortOrder = useCallback(() => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilterStatus('all');
    setFilterType(null);
    setPremiumRange([null, null]);
    setSearchQuery('');
  }, []);

  // Filter policies based on all criteria
  const filteredPolicies = policies.filter(policy => {
    // Status filter
    const matchesStatus = filterStatus === 'all' || policy.status === filterStatus;

    // Search query filter
    const matchesSearch = searchQuery === '' ||
      policy.policyNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      policy.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${policy.clientInfo.firstName} ${policy.clientInfo.lastName}`.toLowerCase().includes(searchQuery.toLowerCase());

    // Type filter
    const matchesType = !filterType || policy.type === filterType;

    // Premium range filter
    const matchesPremiumMin = premiumRange[0] === null || policy.premium >= premiumRange[0];
    const matchesPremiumMax = premiumRange[1] === null || policy.premium <= premiumRange[1];

    return matchesStatus && matchesSearch && matchesType && matchesPremiumMin && matchesPremiumMax;
  });

  // Sort the filtered policies
  const sortedPolicies = [...filteredPolicies].sort((a, b) => {
    if (sortBy === 'date') {
      const dateA = new Date(a.startDate || a.createdAt || '').getTime();
      const dateB = new Date(b.startDate || b.createdAt || '').getTime();
      return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
    } else if (sortBy === 'premium') {
      return sortOrder === 'asc' ? a.premium - b.premium : b.premium - a.premium;
    } else if (sortBy === 'type') {
      return sortOrder === 'asc'
        ? a.type.localeCompare(b.type)
        : b.type.localeCompare(a.type);
    }
    return 0;
  });

  // Group policies by status
  const activePolicies = filteredPolicies.filter(policy => policy.status === 'active');
  const pendingRenewalPolicies = filteredPolicies.filter(policy => policy.status === 'pending_renewal');
  const otherPolicies = filteredPolicies.filter(policy =>
    policy.status !== 'active' && policy.status !== 'pending_renewal'
  );

  // Handle policy selection
  const handlePolicySelect = (policy: Policy) => {
    router.push(`/policies/${policy.id}`);
  };

  // Get status color and icon
  const getStatusInfo = (status: PolicyStatus) => {
    switch (status) {
      case 'active':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Active',
        };
      case 'pending_renewal':
        return {
          color: colors.warning[500],
          bgColor: colors.warning[50],
          icon: <Clock size={16} color={colors.warning[500]} />,
          text: 'Pending Renewal',
        };
      case 'renewed':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Renewed',
        };
      case 'cancelled':
        return {
          color: colors.error[500],
          bgColor: colors.error[50],
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          text: 'Cancelled',
        };
      case 'expired':
        return {
          color: colors.error[500],
          bgColor: colors.error[50],
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          text: 'Expired',
        };
      default:
        return {
          color: colors.textSecondary,
          bgColor: colors.card,
          icon: <Clock size={16} color={colors.textSecondary} />,
          text: status.replace(/_/g, ' '),
        };
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Calculate days until renewal
  const getDaysUntilRenewal = (renewalDate: string) => {
    const today = new Date();
    const renewal = new Date(renewalDate);
    const diffTime = renewal.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Render policy card
  const renderPolicyCard = (policy: Policy, index: number) => {
    const statusInfo = getStatusInfo(policy.status);
    const daysUntilRenewal = getDaysUntilRenewal(policy.renewalDate);

    return (
      <Animated.View
        key={policy.id}
        entering={FadeInDown.delay(100 + index * 100).springify()}
      >
        <TouchableOpacity
          style={styles.policyCard}
          onPress={() => handlePolicySelect(policy)}
          activeOpacity={0.7}
        >
          <View style={styles.policyHeader}>
            <View style={styles.policyIconContainer}>
              <Shield size={24} color={colors.primary[500]} />
            </View>
            <View style={styles.policyHeaderContent}>
              <Text style={styles.policyType}>{policy.type} Insurance</Text>
              <Text style={styles.policyNumber}>{policy.policyNumber}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
              {statusInfo.icon}
              <Text style={[styles.statusText, { color: statusInfo.color }]}>
                {statusInfo.text}
              </Text>
            </View>
          </View>

          <View style={styles.policyInfo}>
            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>Premium:</Text>
              <Text style={styles.policyInfoValue}>
                {formatCurrency(policy.premium, policy.currency)}/{policy.paymentFrequency}
              </Text>
            </View>

            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>Coverage:</Text>
              <Text style={styles.policyInfoValue}>
                {formatCurrency(policy.coverAmount, policy.currency)}
              </Text>
            </View>

            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>Valid Until:</Text>
              <Text style={styles.policyInfoValue}>{formatDate(policy.endDate)}</Text>
            </View>
          </View>

          {policy.status === 'active' && daysUntilRenewal <= 30 && (
            <View style={styles.renewalAlert}>
              <Calendar size={16} color={colors.warning[500]} style={{ marginRight: spacing.xs }} />
              <Text style={styles.renewalAlertText}>
                Renewal due in {daysUntilRenewal} {daysUntilRenewal === 1 ? 'day' : 'days'}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: spacing.md,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    headerActions: {
      flexDirection: 'row',
    },
    iconButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: spacing.xs,
    },
    title: {
      ...typography.h2,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    subtitle: {
      ...typography.body,
      color: colors.textSecondary,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      marginTop: spacing.md,
    },
    searchIcon: {
      marginRight: spacing.xs,
    },
    searchInput: {
      flex: 1,
      ...typography.body,
      color: colors.text,
      paddingVertical: spacing.xs,
    },
    controlsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    filterContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    filterButton: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      borderRadius: borders.radius.md,
      marginRight: spacing.xs,
      marginBottom: spacing.xs,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    filterButtonText: {
      ...typography.caption,
      color: colors.textSecondary,
    },
    filterButtonTextActive: {
      color: colors.white,
    },
    sortContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    sortButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.xs,
    },
    sortByButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
    },
    sortByText: {
      ...typography.caption,
      color: colors.textSecondary,
      marginRight: spacing.xs,
    },
    filterAppliedContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      backgroundColor: colors.info[50],
    },
    filterAppliedText: {
      ...typography.caption,
      color: colors.info[700],
    },
    resetButton: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.sm,
      backgroundColor: colors.info[100],
    },
    resetButtonText: {
      ...typography.caption,
      color: colors.info[700],
    },
    content: {
      flex: 1,
      padding: spacing.md,
    },
    sectionTitle: {
      ...typography.h4,
      color: colors.text,
      marginTop: spacing.md,
      marginBottom: spacing.sm,
    },
    sortIndicator: {
      ...typography.caption,
      color: colors.textSecondary,
      fontWeight: 'normal',
    },
    policyCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      marginBottom: spacing.md,
      overflow: 'hidden',
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    policyHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    policyIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary[50],
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.sm,
    },
    policyHeaderContent: {
      flex: 1,
    },
    policyType: {
      ...typography.h4,
      color: colors.text,
    },
    policyNumber: {
      ...typography.caption,
      color: colors.textSecondary,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
    },
    statusText: {
      ...typography.caption,
      marginLeft: spacing.xs,
      fontWeight: 'bold',
    },
    policyInfo: {
      padding: spacing.md,
    },
    policyInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    policyInfoLabel: {
      ...typography.body,
      color: colors.textSecondary,
    },
    policyInfoValue: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
    },
    renewalAlert: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.warning[50],
      padding: spacing.sm,
      borderTopWidth: 1,
      borderTopColor: colors.warning[200],
    },
    renewalAlertText: {
      ...typography.body,
      color: colors.warning[700],
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
      marginTop: spacing.xl,
    },
    emptyIcon: {
      marginBottom: spacing.md,
    },
    emptyText: {
      ...typography.body,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
    },
    modalContent: {
      width: '100%',
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      maxHeight: '80%',
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
      paddingBottom: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      ...typography.h3,
      color: colors.text,
    },
    modalSection: {
      marginBottom: spacing.md,
    },
    modalSectionTitle: {
      ...typography.h4,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    typeFilterContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    typeFilterButton: {
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borders.radius.md,
      marginRight: spacing.xs,
      marginBottom: spacing.xs,
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: colors.background,
    },
    typeFilterButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    typeFilterButtonText: {
      ...typography.body,
      color: colors.text,
    },
    typeFilterButtonTextActive: {
      color: colors.white,
    },
    premiumRangeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    premiumInputContainer: {
      width: '48%',
    },
    premiumInputLabel: {
      ...typography.caption,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    premiumInput: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    premiumInputText: {
      flex: 1,
      ...typography.body,
      color: colors.text,
      marginLeft: spacing.xs,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
      paddingTop: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    modalResetButton: {
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borders.radius.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    modalResetButtonText: {
      ...typography.body,
      color: colors.textSecondary,
    },
    modalApplyButton: {
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borders.radius.md,
      backgroundColor: colors.primary[500],
    },
    modalApplyButtonText: {
      ...typography.body,
      color: colors.white,
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View>
              <Text style={styles.title}>My Policies</Text>
              <Text style={styles.subtitle}>View and manage your insurance policies</Text>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.iconButton}
                disabled={true}
              >
                <Search size={20} color={colors.textSecondary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconButton}
                disabled={true}
              >
                <Filter size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
        <BottomNavBar />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View>
            <Text style={styles.title}>My Policies</Text>
            <Text style={styles.subtitle}>View and manage your insurance policies</Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => setShowSearchBar(!showSearchBar)}
            >
              <Search size={20} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => setShowFilterModal(true)}
            >
              <Filter size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {showSearchBar && (
          <Animated.View
            style={styles.searchContainer}
            entering={FadeInDown.duration(200)}
          >
            <Search size={20} color={colors.textSecondary} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search policies..."
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery !== '' && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <X size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </Animated.View>
        )}
      </View>

      <View style={styles.controlsContainer}>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'all' && styles.filterButtonActive,
            ]}
            onPress={() => setFilterStatus('all')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'all' && styles.filterButtonTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'active' && styles.filterButtonActive,
            ]}
            onPress={() => setFilterStatus('active')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'active' && styles.filterButtonTextActive,
              ]}
            >
              Active
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'pending_renewal' && styles.filterButtonActive,
            ]}
            onPress={() => setFilterStatus('pending_renewal')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'pending_renewal' && styles.filterButtonTextActive,
              ]}
            >
              Pending Renewal
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.sortContainer}>
          <TouchableOpacity
            style={styles.sortButton}
            onPress={toggleSortOrder}
          >
            {sortOrder === 'asc' ? (
              <SortAsc size={18} color={colors.textSecondary} />
            ) : (
              <SortDesc size={18} color={colors.textSecondary} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.sortByButton}
            onPress={() => {
              // Cycle through sort options
              if (sortBy === 'date') setSortBy('premium');
              else if (sortBy === 'premium') setSortBy('type');
              else setSortBy('date');
            }}
          >
            <Text style={styles.sortByText}>
              Sort: {sortBy === 'date' ? 'Date' : sortBy === 'premium' ? 'Premium' : 'Type'}
            </Text>
            <ChevronDown size={16} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter applied indicator */}
      {(filterType || premiumRange[0] !== null || premiumRange[1] !== null) && (
        <View style={styles.filterAppliedContainer}>
          <Text style={styles.filterAppliedText}>
            Filters applied
          </Text>
          <TouchableOpacity
            style={styles.resetButton}
            onPress={resetFilters}
          >
            <Text style={styles.resetButtonText}>Reset</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeIn.duration(300)}>
          {sortedPolicies.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Shield size={48} color={colors.textSecondary} style={styles.emptyIcon} />
              <Text style={styles.emptyText}>
                No policies found. Your policies will appear here once they are issued.
              </Text>
            </View>
          ) : (
            <>
              {filterStatus === 'all' ? (
                // When showing all policies, display them in sorted order
                <>
                  <Text style={styles.sectionTitle}>
                    Policies ({sortedPolicies.length})
                    {sortBy !== 'date' && (
                      <Text style={styles.sortIndicator}>
                        {' '}• Sorted by {sortBy === 'premium' ? 'premium' : 'type'} ({sortOrder === 'asc' ? 'low to high' : 'high to low'})
                      </Text>
                    )}
                  </Text>
                  {sortedPolicies.map((policy, index) => renderPolicyCard(policy, index))}
                </>
              ) : (
                // When filtering by status, maintain the grouping
                <>
                  {activePolicies.length > 0 && (
                    <>
                      <Text style={styles.sectionTitle}>Active Policies ({activePolicies.length})</Text>
                      {activePolicies
                        .sort((a, b) => {
                          if (sortBy === 'date') {
                            const dateA = new Date(a.startDate || a.createdAt || '').getTime();
                            const dateB = new Date(b.startDate || b.createdAt || '').getTime();
                            return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
                          } else if (sortBy === 'premium') {
                            return sortOrder === 'asc' ? a.premium - b.premium : b.premium - a.premium;
                          } else {
                            return sortOrder === 'asc' ? a.type.localeCompare(b.type) : b.type.localeCompare(a.type);
                          }
                        })
                        .map((policy, index) => renderPolicyCard(policy, index))}
                    </>
                  )}

                  {pendingRenewalPolicies.length > 0 && (
                    <>
                      <Text style={styles.sectionTitle}>Pending Renewal ({pendingRenewalPolicies.length})</Text>
                      {pendingRenewalPolicies
                        .sort((a, b) => {
                          if (sortBy === 'date') {
                            const dateA = new Date(a.renewalDate || '').getTime();
                            const dateB = new Date(b.renewalDate || '').getTime();
                            return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
                          } else if (sortBy === 'premium') {
                            return sortOrder === 'asc' ? a.premium - b.premium : b.premium - a.premium;
                          } else {
                            return sortOrder === 'asc' ? a.type.localeCompare(b.type) : b.type.localeCompare(a.type);
                          }
                        })
                        .map((policy, index) =>
                          renderPolicyCard(policy, index + activePolicies.length)
                        )}
                    </>
                  )}

                  {otherPolicies.length > 0 && (
                    <>
                      <Text style={styles.sectionTitle}>Other Policies ({otherPolicies.length})</Text>
                      {otherPolicies
                        .sort((a, b) => {
                          if (sortBy === 'date') {
                            const dateA = new Date(a.startDate || a.createdAt || '').getTime();
                            const dateB = new Date(b.startDate || b.createdAt || '').getTime();
                            return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
                          } else if (sortBy === 'premium') {
                            return sortOrder === 'asc' ? a.premium - b.premium : b.premium - a.premium;
                          } else {
                            return sortOrder === 'asc' ? a.type.localeCompare(b.type) : b.type.localeCompare(a.type);
                          }
                        })
                        .map((policy, index) =>
                          renderPolicyCard(policy, index + activePolicies.length + pendingRenewalPolicies.length)
                        )}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </Animated.View>
      </ScrollView>

      <BottomNavBar />

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            style={styles.modalContent}
            entering={FadeInDown.duration(300)}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Policies</Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>Policy Type</Text>
              <View style={styles.typeFilterContainer}>
                {['motor', 'houseowners', 'household', 'allrisks', 'life'].map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.typeFilterButton,
                      filterType === type && styles.typeFilterButtonActive,
                    ]}
                    onPress={() => setFilterType(filterType === type ? null : type)}
                  >
                    <Text
                      style={[
                        styles.typeFilterButtonText,
                        filterType === type && styles.typeFilterButtonTextActive,
                      ]}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>Premium Range</Text>
              <View style={styles.premiumRangeContainer}>
                <View style={styles.premiumInputContainer}>
                  <Text style={styles.premiumInputLabel}>Min</Text>
                  <View style={styles.premiumInput}>
                    <DollarSign size={16} color={colors.textSecondary} />
                    <TextInput
                      style={styles.premiumInputText}
                      placeholder="0"
                      placeholderTextColor={colors.textSecondary}
                      keyboardType="numeric"
                      value={premiumRange[0] !== null ? String(premiumRange[0]) : ''}
                      onChangeText={(text) => {
                        const value = text === '' ? null : Number(text);
                        setPremiumRange([value, premiumRange[1]]);
                      }}
                    />
                  </View>
                </View>

                <View style={styles.premiumInputContainer}>
                  <Text style={styles.premiumInputLabel}>Max</Text>
                  <View style={styles.premiumInput}>
                    <DollarSign size={16} color={colors.textSecondary} />
                    <TextInput
                      style={styles.premiumInputText}
                      placeholder="Any"
                      placeholderTextColor={colors.textSecondary}
                      keyboardType="numeric"
                      value={premiumRange[1] !== null ? String(premiumRange[1]) : ''}
                      onChangeText={(text) => {
                        const value = text === '' ? null : Number(text);
                        setPremiumRange([premiumRange[0], value]);
                      }}
                    />
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalResetButton}
                onPress={resetFilters}
              >
                <Text style={styles.modalResetButtonText}>Reset All</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalApplyButton}
                onPress={() => setShowFilterModal(false)}
              >
                <Text style={styles.modalApplyButtonText}>Apply Filters</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}
