import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { useAppDispatch } from '@/store/hooks';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { fetchProfile } from '@/store/profileSlice';
import ProfileForm from '@/components/profile/ProfileForm';

export default function EditProfileScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  // Fetch profile on component mount
  useEffect(() => {
    dispatch(fetchProfile());
  }, [dispatch]);

  const handleSuccess = () => {
    router.back();
  };

  const handleCancel = () => {
    router.back();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
      </View>

      <ProfileForm onSuccess={handleSuccess} onCancel={handleCancel} />
    </SafeAreaView>
  );
}