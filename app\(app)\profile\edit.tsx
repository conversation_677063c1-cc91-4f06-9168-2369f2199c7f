import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { router } from 'expo-router';
import { ArrowLeft, User, Mail, Phone, MapPin, Briefcase, Building, CreditCard, UserCheck, FileText } from 'lucide-react-native';
import { updateProfile, UserType } from '@/store/authSlice';
import Animated, { FadeInDown } from 'react-native-reanimated';

export default function EditProfileScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  // Get user from Redux store
  const user = useAppSelector(state => state.auth.user);

  // Get user type
  const userType = user?.userType || 'individual';

  // Common form state
  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [address, setAddress] = useState(user?.address || '');
  const [isLoading, setIsLoading] = useState(false);

  // Individual user form state
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [lastName, setLastName] = useState(user?.lastName || '');
  const [idNumber, setIdNumber] = useState(user?.idNumber || '');
  const [occupation, setOccupation] = useState(user?.occupation || '');

  // Business user form state
  const [companyName, setCompanyName] = useState(user?.companyName || '');
  const [registrationNumber, setRegistrationNumber] = useState(user?.registrationNumber || '');
  const [contactPersonName, setContactPersonName] = useState(user?.contactPersonName || '');
  const [industry, setIndustry] = useState(user?.industry || '');
  const [taxNumber, setTaxNumber] = useState(user?.taxNumber || '');

  // Handle save changes
  const handleSaveChanges = async () => {
    try {
      setIsLoading(true);

      // Validate common fields
      if (!email.trim()) {
        Alert.alert('Error', 'Email is required');
        return;
      }

      // Simple email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        Alert.alert('Error', 'Please enter a valid email address');
        return;
      }

      if (!phone.trim()) {
        Alert.alert('Error', 'Phone number is required');
        return;
      }

      // Validate user type specific fields
      if (userType === 'individual') {
        if (!firstName.trim()) {
          Alert.alert('Error', 'First name is required');
          return;
        }

        if (!lastName.trim()) {
          Alert.alert('Error', 'Last name is required');
          return;
        }
      } else {
        if (!companyName.trim()) {
          Alert.alert('Error', 'Company name is required');
          return;
        }

        if (!registrationNumber.trim()) {
          Alert.alert('Error', 'Registration number is required');
          return;
        }

        if (!contactPersonName.trim()) {
          Alert.alert('Error', 'Contact person name is required');
          return;
        }
      }

      // Create profile data based on user type
      let profileData = {
        email,
        phone,
        address,
      };

      if (userType === 'individual') {
        profileData = {
          ...profileData,
          firstName,
          lastName,
          idNumber,
          occupation,
        };
      } else {
        profileData = {
          ...profileData,
          companyName,
          registrationNumber,
          contactPersonName,
          industry,
          taxNumber,
        };
      }

      // Dispatch action to update profile
      const result = await dispatch(updateProfile(profileData));

      if (result.meta.requestStatus === 'fulfilled') {
        Alert.alert(
          'Success',
          'Profile updated successfully',
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to update profile. Please try again.');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
      marginBottom: Platform.OS === 'ios' ? 88 : 60, 
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    inputContainer: {
      marginBottom: spacing.lg,
    },
    inputLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      backgroundColor: colors.card,
    },
    input: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
      textAlignVertical: 'top',
    },
    saveButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.md,
      marginBottom: spacing.lg,
    },
    saveButtonText: {
      color: colors.white,
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Common Fields */}
          <Animated.Text
            style={styles.sectionTitle}
            entering={FadeInDown.delay(50).springify()}
          >
            Contact Information
          </Animated.Text>

          <Animated.View
            style={styles.inputContainer}
            entering={FadeInDown.delay(100).springify()}
          >
            <Text style={styles.inputLabel}>Email</Text>
            <View style={styles.inputWrapper}>
              <Mail size={20} color={colors.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Email address"
                placeholderTextColor={colors.textSecondary}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
          </Animated.View>

          <Animated.View
            style={styles.inputContainer}
            entering={FadeInDown.delay(150).springify()}
          >
            <Text style={styles.inputLabel}>Phone</Text>
            <View style={styles.inputWrapper}>
              <Phone size={20} color={colors.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Phone number"
                placeholderTextColor={colors.textSecondary}
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>
          </Animated.View>

          <Animated.View
            style={styles.inputContainer}
            entering={FadeInDown.delay(200).springify()}
          >
            <Text style={styles.inputLabel}>Address</Text>
            <View style={styles.inputWrapper}>
              <MapPin size={20} color={colors.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Your address"
                placeholderTextColor={colors.textSecondary}
                value={address}
                onChangeText={setAddress}
                multiline={true}
                numberOfLines={2}
              />
            </View>
          </Animated.View>

          {/* Individual User Fields */}
          {userType === 'individual' && (
            <>
              <Animated.Text
                style={styles.sectionTitle}
                entering={FadeInDown.delay(250).springify()}
              >
                Personal Information
              </Animated.Text>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(300).springify()}
              >
                <Text style={styles.inputLabel}>First Name</Text>
                <View style={styles.inputWrapper}>
                  <User size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="First name"
                    placeholderTextColor={colors.textSecondary}
                    value={firstName}
                    onChangeText={setFirstName}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(350).springify()}
              >
                <Text style={styles.inputLabel}>Last Name</Text>
                <View style={styles.inputWrapper}>
                  <User size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Last name"
                    placeholderTextColor={colors.textSecondary}
                    value={lastName}
                    onChangeText={setLastName}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(400).springify()}
              >
                <Text style={styles.inputLabel}>ID Number</Text>
                <View style={styles.inputWrapper}>
                  <CreditCard size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="ID/Passport number"
                    placeholderTextColor={colors.textSecondary}
                    value={idNumber}
                    onChangeText={setIdNumber}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(450).springify()}
              >
                <Text style={styles.inputLabel}>Occupation</Text>
                <View style={styles.inputWrapper}>
                  <Briefcase size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Your occupation"
                    placeholderTextColor={colors.textSecondary}
                    value={occupation}
                    onChangeText={setOccupation}
                  />
                </View>
              </Animated.View>
            </>
          )}

          {/* Business User Fields */}
          {userType === 'business' && (
            <>
              <Animated.Text
                style={styles.sectionTitle}
                entering={FadeInDown.delay(250).springify()}
              >
                Business Information
              </Animated.Text>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(300).springify()}
              >
                <Text style={styles.inputLabel}>Company Name</Text>
                <View style={styles.inputWrapper}>
                  <Building size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Company name"
                    placeholderTextColor={colors.textSecondary}
                    value={companyName}
                    onChangeText={setCompanyName}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(350).springify()}
              >
                <Text style={styles.inputLabel}>Registration Number</Text>
                <View style={styles.inputWrapper}>
                  <FileText size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Registration number"
                    placeholderTextColor={colors.textSecondary}
                    value={registrationNumber}
                    onChangeText={setRegistrationNumber}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(400).springify()}
              >
                <Text style={styles.inputLabel}>Contact Person</Text>
                <View style={styles.inputWrapper}>
                  <UserCheck size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Contact person name"
                    placeholderTextColor={colors.textSecondary}
                    value={contactPersonName}
                    onChangeText={setContactPersonName}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(450).springify()}
              >
                <Text style={styles.inputLabel}>Tax Number</Text>
                <View style={styles.inputWrapper}>
                  <CreditCard size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Tax number (optional)"
                    placeholderTextColor={colors.textSecondary}
                    value={taxNumber}
                    onChangeText={setTaxNumber}
                  />
                </View>
              </Animated.View>

              <Animated.View
                style={styles.inputContainer}
                entering={FadeInDown.delay(500).springify()}
              >
                <Text style={styles.inputLabel}>Industry</Text>
                <View style={styles.inputWrapper}>
                  <Briefcase size={20} color={colors.textSecondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Industry (optional)"
                    placeholderTextColor={colors.textSecondary}
                    value={industry}
                    onChangeText={setIndustry}
                  />
                </View>
              </Animated.View>
            </>
          )}

          <Animated.View
            entering={FadeInDown.delay(400).springify()}
          >
            <TouchableOpacity
              style={[
                styles.saveButton,
                isLoading && { opacity: 0.7 }
              ]}
              onPress={handleSaveChanges}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <Text style={styles.saveButtonText}>Save Changes</Text>
              )}
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}