import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { router } from 'expo-router';
import { ArrowLeft, FileText, Upload, Check, Clock, AlertCircle, Camera, File } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import Animated, { FadeInDown } from 'react-native-reanimated';

// Define document type
type Document = {
  id: string;
  name: string;
  type: string;
  status: string;
  date: string;
  reason?: string;
  uri?: string;
  fileName?: string;
  fileType?: string;
  isImage?: boolean;
};

export default function KycDocumentsScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // State for documents and loading
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.md,
    },
    documentCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderLeftWidth: 4,
    },
    documentHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    documentName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      flex: 1,
    },
    documentType: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.full,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs,
    },
    documentDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: spacing.sm,
    },
    reasonContainer: {
      backgroundColor: colors.error[50],
      padding: spacing.sm,
      borderRadius: borders.radius.md,
      marginTop: spacing.sm,
    },
    reasonText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.error[700],
    },
    uploadButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.md,
    },
    uploadButtonText: {
      color: colors.white,
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginLeft: spacing.sm,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.xl * 2,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
    },
    documentFileName: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
    documentIconRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.sm,
      gap: spacing.xs,
    },
  });

  // Handle document upload
  const handleUploadDocument = () => {
    Alert.alert(
      'Upload Document',
      'Select document type to upload',
      [
        {
          text: 'National ID',
          onPress: () => selectUploadMethod('National ID', 'ID'),
        },
        {
          text: 'Proof of Address',
          onPress: () => selectUploadMethod('Proof of Address', 'Utility Bill'),
        },
        {
          text: 'Bank Statement',
          onPress: () => selectUploadMethod('Bank Statement', 'Financial'),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  // Select upload method (camera, gallery, or document)
  const selectUploadMethod = (documentName: string, documentType: string) => {
    Alert.alert(
      'Upload Method',
      'How would you like to upload your document?',
      [
        {
          text: 'Take Photo',
          onPress: () => pickImage(documentName, documentType, true),
        },
        {
          text: 'Choose from Gallery',
          onPress: () => pickImage(documentName, documentType, false),
        },
        {
          text: 'Select Document File',
          onPress: () => pickDocument(documentName, documentType),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  // Pick image from camera or gallery
  const pickImage = async (documentName: string, documentType: string, useCamera: boolean) => {
    try {
      let permissionResult;

      if (useCamera) {
        permissionResult = await ImagePicker.requestCameraPermissionsAsync();
        if (permissionResult.granted === false) {
          Alert.alert('Permission Required', 'Please enable camera access');
          return;
        }
      } else {
        permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (permissionResult.granted === false) {
          Alert.alert('Permission Required', 'Please enable photo library access');
          return;
        }
      }

      setIsUploading(true);

      const result = useCamera
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 0.8,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 0.8,
          });

      if (!result.canceled) {
        console.log('Selected image:', result.assets[0].uri);

        // Create a new document object
        const newDocument = {
          id: Date.now().toString(),
          name: documentName,
          type: documentType,
          status: 'pending',
          date: new Date().toISOString().split('T')[0],
          uri: result.assets[0].uri,
          isImage: true,
        };

        // Add the new document to the list
        setDocuments(prevDocuments => [newDocument, ...prevDocuments]);

        Alert.alert(
          'Success',
          'Document uploaded successfully and is pending verification',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to upload document. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Pick document file
  const pickDocument = async (documentName: string, documentType: string) => {
    try {
      setIsUploading(true);

      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        copyToCacheDirectory: true,
      });

      if (result.canceled === false) {
        console.log('Selected document:', result.assets[0]);

        // Create a new document object
        const newDocument = {
          id: Date.now().toString(),
          name: documentName,
          type: documentType,
          status: 'pending',
          date: new Date().toISOString().split('T')[0],
          uri: result.assets[0].uri,
          fileName: result.assets[0].name,
          fileType: result.assets[0].mimeType,
          isImage: false,
        };

        // Add the new document to the list
        setDocuments(prevDocuments => [newDocument, ...prevDocuments]);

        Alert.alert(
          'Success',
          'Document uploaded successfully and is pending verification',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error picking document:', error);
      Alert.alert('Error', 'Failed to upload document. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Render document status
  const renderDocumentStatus = (status: string) => {
    let statusColor, icon, text;

    switch (status) {
      case 'verified':
        statusColor = colors.success[500];
        icon = <Check size={14} color={colors.success[500]} />;
        text = 'Verified';
        break;
      case 'pending':
        statusColor = colors.warning[500];
        icon = <Clock size={14} color={colors.warning[500]} />;
        text = 'Pending';
        break;
      case 'rejected':
        statusColor = colors.error[500];
        icon = <AlertCircle size={14} color={colors.error[500]} />;
        text = 'Rejected';
        break;
      default:
        statusColor = colors.textSecondary;
        icon = null;
        text = 'Unknown';
    }

    return (
      <View style={[
        styles.statusContainer,
        { backgroundColor: `${statusColor}20` }
      ]}>
        {icon}
        <Text style={[styles.statusText, { color: statusColor }]}>{text}</Text>
      </View>
    );
  };



  // Render document item
  const renderDocumentItem = ({ item }: { item: Document }) => {
    let borderColor;

    switch (item.status) {
      case 'verified':
        borderColor = colors.success[500];
        break;
      case 'pending':
        borderColor = colors.warning[500];
        break;
      case 'rejected':
        borderColor = colors.error[500];
        break;
      default:
        borderColor = colors.border;
    }

    return (
      <View style={[styles.documentCard, { borderLeftColor: borderColor }]}>
        <View style={styles.documentHeader}>
          <View style={{ flex: 1 }}>
            <Text style={styles.documentName}>{item.name}</Text>
            <Text style={styles.documentType}>{item.type}</Text>
            {item.fileName && (
              <Text style={styles.documentFileName}>
                File: {item.fileName}
              </Text>
            )}
          </View>
          {renderDocumentStatus(item.status)}
        </View>

        <View style={styles.documentIconRow}>
          {item.isImage === false ? (
            <File size={16} color={colors.primary[500]} />
          ) : (
            <Camera size={16} color={colors.primary[500]} />
          )}
          <Text style={styles.documentDate}>Submitted: {item.date}</Text>
        </View>

        {item.reason && (
          <View style={styles.reasonContainer}>
            <Text style={styles.reasonText}>Reason: {item.reason}</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>KYC Documents</Text>
      </View>

      <View style={styles.content}>
        <Animated.Text
          style={styles.sectionTitle}
          entering={FadeInDown.delay(100).springify()}
        >
          Your Documents
        </Animated.Text>

        {documents.length > 0 ? (
          <Animated.View
            entering={FadeInDown.delay(200).springify()}
            style={{ flex: 1 }}
          >
            <FlatList
              data={documents}
              renderItem={renderDocumentItem}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
            />
          </Animated.View>
        ) : (
          <Animated.View
            style={styles.emptyContainer}
            entering={FadeInDown.delay(200).springify()}
          >
            <FileText size={48} color={colors.textSecondary} />
            <Text style={styles.emptyText}>
              You haven't uploaded any documents yet
            </Text>
          </Animated.View>
        )}

        <Animated.View
          entering={FadeInDown.delay(300).springify()}
        >
          <TouchableOpacity
            style={[
              styles.uploadButton,
              isUploading && { opacity: 0.7 }
            ]}
            onPress={handleUploadDocument}
            disabled={isUploading}
          >
            {isUploading ? (
              <ActivityIndicator color={colors.white} />
            ) : (
              <>
                <Upload size={20} color={colors.white} />
                <Text style={styles.uploadButtonText}>Upload New Document</Text>
              </>
            )}
          </TouchableOpacity>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}
