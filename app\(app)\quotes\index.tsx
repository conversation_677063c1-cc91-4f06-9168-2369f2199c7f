import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { FileText, PlusCircle, History } from 'lucide-react-native';
import { router, useFocusEffect } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import InsuranceProductCard from '@/components/quotes/InsuranceProductCard';
import QuotationSummaryCard from '@/components/quotes/QuotationSummaryCard';
import { mockInsuranceProducts, InsuranceProductType } from '@/types/quote.types';
import useQuoteStore from '@/store/quoteStore';
import { showToast } from '@/utils/toast';
import TabNavigation from '@/components/navigation/TabNavigation';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function QuotesScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [activeTab, setActiveTab] = useState('new');

  // Get quotes from the store
  const { quotes, fetchQuotes, createQuote, setCurrentQuote, isLoading } = useQuoteStore();

  // Fetch quotes on component mount
  useEffect(() => {
    fetchQuotes();
  }, [fetchQuotes]);

  // Use useFocusEffect to refresh quotes when the screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('Quotes screen focused, refreshing quotes');
      fetchQuotes();

      // Return a cleanup function (optional)
      return () => {
        console.log('Quotes screen unfocused');
      };
    }, [fetchQuotes])
  );

  // Handle selecting an insurance product
  const handleSelectProduct = async (type: string) => {
    try {
      console.log('Creating quote for type:', type);

      // Validate type before proceeding
      if (!type) {
        throw new Error('Invalid insurance type: empty');
      }

      // Create a new quote
      const newQuote = await createQuote(type as InsuranceProductType);

      if (!newQuote || !newQuote.id) {
        throw new Error('Failed to create quote: Invalid quote data');
      }

      console.log('Quote created successfully:', newQuote.id);

      // Navigate to the general info page with a slight delay to ensure state is updated
      setTimeout(() => {
        try {
          router.push({
            pathname: '/quotes/general-info',
            params: { quoteId: newQuote.id }
          });
        } catch (navError) {
          console.error('Navigation error:', navError);
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate to quote form. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    } catch (error) {
      console.error('Error creating quote:', error);
      // Show error toast instead of just logging to console
      showToast(
        'error',
        'Error',
        'Failed to create quote. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle viewing quote details
  const handleViewQuoteDetails = (quote: any) => {
    // Set the current quote in the store
    setCurrentQuote(quote);

    // If the quote is a draft, navigate to the last edited form or general info
    if (quote.status === 'draft') {
      // Check if client info is complete
      const isClientInfoComplete = quote.clientInfo &&
        quote.clientInfo.firstName &&
        quote.clientInfo.lastName &&
        quote.clientInfo.email &&
        quote.clientInfo.phone;

      if (!isClientInfoComplete) {
        // Navigate to the general info page if client info is incomplete
        router.push({
          pathname: '/quotes/general-info',
          params: { quoteId: quote.id }
        });
      } else {
        // Navigate directly to the dynamic route
        // This is the most reliable approach
        console.log('Navigating to quote form with:', {
          type: quote.type,
          quoteId: quote.id
        });

        // For specific quote types, navigate to their dedicated forms
        if (quote.type === 'motor') {
          router.push({
            pathname: '/(app)/quotes/motor/form',
            params: {
              quoteId: quote.id
            }
          });
        } else if (quote.type === 'life') {
          router.push({
            pathname: '/(app)/quotes/life/form',
            params: {
              quoteId: quote.id
            }
          });
        } else if (quote.type === 'allRisks') {
          router.push({
            pathname: '/(app)/quotes/allRisks/form',
            params: {
              quoteId: quote.id
            }
          });
        } else if (quote.type === 'houseowners') {
          router.push({
            pathname: '/(app)/quotes/houseowners/form',
            params: {
              quoteId: quote.id
            }
          });
        } else if (quote.type === 'householdContents') {
          router.push({
            pathname: '/(app)/quotes/householdContents/form',
            params: {
              quoteId: quote.id
            }
          });
        } else {
          // For other types, use the dynamic route
          router.push({
            pathname: '/(app)/quotes/[type]',
            params: {
              type: quote.type,
              quoteId: quote.id
            }
          });
        }
      }
    } else {
      // For non-draft quotes, navigate to the summary page
      router.push({
        pathname: '/quotes/[type]/summary',
        params: {
          type: quote.type,
          quoteId: quote.id
        }
      });
    }
  };

  // Handle viewing quote PDF
  const handleViewQuotePdf = (quote: any) => {
    // Set the current quote in the store
    setCurrentQuote(quote);

    // Navigate to the PDF page using the dynamic route
    router.push({
      pathname: '/quotes/[type]/pdf',
      params: {
        type: quote.type,
        quoteId: quote.id
      }
    });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xl,
      color: colors.text,
      marginLeft: spacing.md,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
    },
    scrollViewContent: {
      paddingBottom: spacing.xl * 2, // Add extra padding for bottom nav bar
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    newQuoteButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      borderRadius: borders.radius.md,
      alignSelf: 'flex-start',
      marginBottom: spacing.lg,
    },
    newQuoteButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
    },
  });

  // Define tabs
  const tabs = [
    {
      id: 'new',
      title: 'New Quote',
      icon: <PlusCircle size={16} color={activeTab === 'new' ? colors.primary[500] : colors.textSecondary} />
    },
    {
      id: 'quotes',
      title: 'Your Quotes',
      icon: <History size={16} color={activeTab === 'quotes' ? colors.primary[500] : colors.textSecondary} />
    }
  ];

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Render content based on active tab
  const renderTabContent = () => {
    if (activeTab === 'new') {
      return (
        <>
          <Animated.View entering={FadeInDown.delay(100).springify()}>
            <Text style={styles.sectionTitle}>Get a New Quote</Text>
          </Animated.View>

          {mockInsuranceProducts.map((product, index) => (
            <InsuranceProductCard
              key={product.id}
              id={product.id}
              type={product.type}
              name={product.name}
              description={product.description}
              color={product.color}
              onSelect={handleSelectProduct}
              delay={index}
            />
          ))}
        </>
      );
    } else {
      return (
        <>
          <Animated.View entering={FadeInDown.delay(100).springify()}>
            <Text style={styles.sectionTitle}>Your Quotes</Text>
          </Animated.View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary[500]} />
            </View>
          ) : quotes.length > 0 ? (
            quotes.map((quote) => (
              <QuotationSummaryCard
                key={quote.id}
                quote={quote}
                onViewDetails={handleViewQuoteDetails}
                onViewPdf={quote.pdfUrl ? handleViewQuotePdf : undefined}
              />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <FileText size={48} color={colors.textSecondary} />
              <Text style={styles.emptyText}>
                You don't have any quotes yet. Get started by requesting a new quote.
              </Text>
            </View>
          )}
        </>
      );
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Insurance Quotes</Text>
      </View>

      <View style={styles.content}>
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollViewContent}
        >
          {renderTabContent()}
        </ScrollView>
      </View>

      {/* Bottom Navigation Bar */}
      <BottomNavBar currentRoute="home" />
    </SafeAreaView>
  );
}
