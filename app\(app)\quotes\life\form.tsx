import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { User, Calendar, DollarSign, Clock, Briefcase, Heart, Plus, Trash2, Users, Activity } from 'lucide-react-native';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import QuoteDocumentsSection from '@/components/quotes/QuoteDocumentsSection';
import BeneficiaryManager, { Beneficiary } from '@/components/quotes/BeneficiaryManager';
import HealthQuestionnaire, { HealthCondition } from '@/components/quotes/HealthQuestionnaire';
import PremiumBreakdown from '@/components/quotes/PremiumBreakdown';
import useQuoteStore from '@/store/quoteStore';
import { QuoteDocument } from '@/types/quote.types';
import { showToast } from '@/utils/toast';
import { calculateLifeAssurancePremium } from '@/utils/quoteCalculations';

export default function LifeAssuranceQuoteForm() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const { quoteId } = useLocalSearchParams();

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    isLoading: isQuoteLoading,
    setCurrentQuote,
    currentQuote
  } = useQuoteStore();

  // Form state
  const [formData, setFormData] = useState({
    insuredPerson: {
      dateOfBirth: '',
      gender: 'male',
      smoker: false,
      occupation: '',
      occupationRisk: 'medium', // Default occupation risk
      healthConditions: [] as HealthCondition[],
      healthRiskLevel: 'none' // Default health risk level
    },
    coverageAmount: 0,
    term: 10, // Default term of 10 years
    beneficiaries: [] as Beneficiary[]
  });

  // Documents state
  const [documents, setDocuments] = useState<QuoteDocument[]>([]);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);

  // Premium calculation state
  const [premiumBreakdown, setPremiumBreakdown] = useState<Record<string, number>>({});
  const [showPremiumBreakdown, setShowPremiumBreakdown] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState('details');

  // Load quote data if editing an existing quote
  useEffect(() => {
    const quoteIdToUse = quoteId as string;

    if (!quoteIdToUse) {
      router.replace('/(app)/quotes');
      return;
    }

    console.log('LifeAssuranceQuoteForm - Looking up quote with ID:', quoteIdToUse);
    const quote = getQuoteById(quoteIdToUse);

    if (quote) {
      console.log('LifeAssuranceQuoteForm - Quote found:', quote.id, 'Type:', quote.type);
      setCurrentQuote(quote);

      // Initialize form data based on existing quote data
      if (quote.additionalInfo) {
        setFormData(quote.additionalInfo);

        // If premium breakdown exists, set it
        if (quote.additionalInfo.premiumBreakdown) {
          setPremiumBreakdown(quote.additionalInfo.premiumBreakdown);
          setShowPremiumBreakdown(true);
        }
      } else {
        // Set default form data
        setFormData({
          insuredPerson: {
            dateOfBirth: '',
            gender: 'male',
            smoker: false,
            occupation: '',
            occupationRisk: 'medium',
            healthConditions: [],
            healthRiskLevel: 'none'
          },
          coverageAmount: 0,
          term: 10,
          beneficiaries: []
        });
      }

      // Initialize documents if they exist
      if (quote.documents && quote.documents.length > 0) {
        setDocuments(quote.documents);
      }
    } else {
      showToast(
        'error',
        'Error',
        'Quote not found',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
    }
  }, [quoteId]);

  // Update form field
  const updateFormField = (field: string, value: any) => {
    if (field.includes('.')) {
      // Handle nested fields like 'insuredPerson.dateOfBirth'
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Validate form
  useEffect(() => {
    validateForm();
  }, [formData, documents]);

  // Validate form - memoize to prevent unnecessary re-renders
  const validateForm = React.useCallback(() => {
    const newErrors: Record<string, string> = {};

    // Validate date of birth
    if (!formData.insuredPerson.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required';
    }

    // Validate coverage amount
    if (!formData.coverageAmount || formData.coverageAmount <= 0) {
      newErrors.coverageAmount = 'Coverage amount is required and must be greater than 0';
    }

    // Validate term
    if (!formData.term || formData.term <= 0) {
      newErrors.term = 'Term is required and must be greater than 0';
    }

    // Validate occupation
    if (!formData.insuredPerson.occupation) {
      newErrors.occupation = 'Occupation is required';
    }

    // Validate beneficiaries
    if (formData.beneficiaries.length === 0) {
      newErrors.beneficiaries = 'At least one beneficiary is required';
    } else {
      // Check if beneficiary percentages add up to 100%
      const totalPercentage = formData.beneficiaries.reduce((sum, b) => sum + b.percentage, 0);
      if (totalPercentage !== 100) {
        newErrors.beneficiaries = `Beneficiary percentages must add up to 100%. Current total: ${totalPercentage}%`;
      }
    }

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      newErrors.documents = 'All required documents must be uploaded';
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  }, [formData, documents]);

  // Handle document updates
  const handleDocumentsUpdated = (updatedDocuments: QuoteDocument[]) => {
    setDocuments(updatedDocuments);
  };

  // Handle beneficiaries updates
  const handleBeneficiariesUpdated = (updatedBeneficiaries: Beneficiary[]) => {
    setFormData(prev => ({
      ...prev,
      beneficiaries: updatedBeneficiaries
    }));
  };

  // Handle health conditions updates
  const handleHealthConditionsUpdated = (updatedConditions: HealthCondition[]) => {
    setFormData(prev => ({
      ...prev,
      insuredPerson: {
        ...prev.insuredPerson,
        healthConditions: updatedConditions
      }
    }));
  };

  // Handle health risk level updates
  const handleRiskLevelUpdated = (riskLevel: string) => {
    setFormData(prev => ({
      ...prev,
      insuredPerson: {
        ...prev.insuredPerson,
        healthRiskLevel: riskLevel
      }
    }));
  };

  // Calculate premium
  const calculatePremium = React.useCallback(() => {
    if (!formData.insuredPerson.dateOfBirth || !formData.coverageAmount) {
      return { premium: 0, breakdown: {} };
    }

    return calculateLifeAssurancePremium(
      formData.coverageAmount,
      formData.insuredPerson.dateOfBirth,
      formData.insuredPerson.smoker,
      formData.term,
      formData.insuredPerson.occupationRisk,
      formData.insuredPerson.healthRiskLevel
    );
  }, [
    formData.coverageAmount,
    formData.insuredPerson.dateOfBirth,
    formData.insuredPerson.smoker,
    formData.term,
    formData.insuredPerson.occupationRisk,
    formData.insuredPerson.healthRiskLevel
  ]);

  // Update premium breakdown when relevant form fields change
  useEffect(() => {
    const { premium, breakdown } = calculatePremium();
    setPremiumBreakdown(breakdown);

    // Show premium breakdown if we have a valid premium
    if (premium > 0) {
      setShowPremiumBreakdown(true);
    }
  }, [
    formData.coverageAmount,
    formData.insuredPerson.dateOfBirth,
    formData.insuredPerson.smoker,
    formData.term,
    formData.insuredPerson.occupationRisk,
    formData.insuredPerson.healthRiskLevel,
    calculatePremium
  ]);

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  // Handle next button
  const handleNext = async () => {
    if (!formValid) {
      showToast(
        'error',
        'Validation Error',
        'Please correct the errors before proceeding',
        { visibilityTime: 4000 }
      );
      return;
    }

    try {
      // Calculate premium based on coverage amount, age, smoker status, etc.
      const { premium, breakdown } = calculatePremium();

      // Save form data, documents, and premium breakdown
      await updateQuote({
        additionalInfo: {
          ...formData,
          premiumBreakdown: breakdown
        },
        documents: documents,
        premium: premium,
        currency: 'P', // Set Botswana currency
        coverAmount: formData.coverageAmount,
        // Update the timestamp
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Navigate to summary page
      router.push(`/quotes/${currentQuote.type}/summary?quoteId=${currentQuote.id}`);
    } catch (error) {
      console.error('Error saving quote:', error);
      showToast(
        'error',
        'Error',
        'Failed to save quote. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle save button
  const handleSave = async () => {
    try {
      // Calculate premium for saving
      const { premium, breakdown } = calculatePremium();

      // Save form data, documents, and premium breakdown
      await updateQuote({
        additionalInfo: {
          ...formData,
          premiumBreakdown: breakdown
        },
        documents: documents,
        premium: premium,
        // Update the timestamp
        updatedAt: new Date().toISOString().split('T')[0]
      });

      showToast(
        'success',
        'Success',
        'Quote saved successfully',
        { visibilityTime: 3000 }
      );

      // Navigate back to quotes list
      router.push('/(app)/quotes');
    } catch (error) {
      console.error('Error saving quote:', error);
      showToast(
        'error',
        'Error',
        'Failed to save quote. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  // Render life assurance form fields
  const renderFormFields = () => {
    const genderOptions: SelectOption[] = [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'other', label: 'Other' },
    ];

    const termOptions: SelectOption[] = [
      { value: '5', label: '5 years' },
      { value: '10', label: '10 years' },
      { value: '15', label: '15 years' },
      { value: '20', label: '20 years' },
      { value: '25', label: '25 years' },
      { value: '30', label: '30 years' },
    ];

    const occupationRiskOptions: SelectOption[] = [
      { value: 'low', label: 'Low Risk (e.g., Office Worker, Teacher)' },
      { value: 'medium', label: 'Medium Risk (e.g., Retail, Service Industry)' },
      { value: 'high', label: 'High Risk (e.g., Construction, Manufacturing)' },
      { value: 'very_high', label: 'Very High Risk (e.g., Mining, Offshore Oil)' },
    ];

    return (
      <View>
        <Text style={styles.sectionTitle}>Insured Person Details</Text>

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.insuredPerson.dateOfBirth}
          onChange={(value) => updateFormField('insuredPerson.dateOfBirth', value)}
          placeholder="Select date of birth"
          error={errors.dateOfBirth}
          required
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Gender"
          type="select"
          value={formData.insuredPerson.gender}
          onChange={(value) => updateFormField('insuredPerson.gender', value)}
          options={genderOptions}
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Smoker"
          type="switch"
          value={formData.insuredPerson.smoker}
          onChange={(value) => updateFormField('insuredPerson.smoker', value)}
        />

        <DynamicFormField
          label="Occupation"
          type="text"
          value={formData.insuredPerson.occupation}
          onChange={(value) => updateFormField('insuredPerson.occupation', value)}
          placeholder="Enter occupation"
          error={errors.occupation}
          required
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Occupation Risk Level"
          type="select"
          value={formData.insuredPerson.occupationRisk}
          onChange={(value) => updateFormField('insuredPerson.occupationRisk', value)}
          options={occupationRiskOptions}
          icon={<Activity size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Coverage Details</Text>

        <DynamicFormField
          label="Coverage Amount"
          type="number"
          value={formData.coverageAmount?.toString()}
          onChange={(value) => updateFormField('coverageAmount', parseFloat(value) || 0)}
          placeholder="e.g., 500000"
          error={errors.coverageAmount}
          required
          keyboardType="numeric"
          icon={<DollarSign size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Term"
          type="select"
          value={formData.term?.toString()}
          onChange={(value) => updateFormField('term', parseInt(value) || 10)}
          options={termOptions}
          error={errors.term}
          required
          icon={<Clock size={20} color={colors.textSecondary} />}
        />

        {/* Health Questionnaire */}
        <Text style={styles.sectionTitle}>Health Assessment</Text>
        <HealthQuestionnaire
          healthConditions={formData.insuredPerson.healthConditions}
          onHealthConditionsUpdated={handleHealthConditionsUpdated}
          onRiskLevelUpdated={handleRiskLevelUpdated}
        />

        {/* Premium Breakdown */}
        {showPremiumBreakdown && (
          <PremiumBreakdown
            breakdown={premiumBreakdown}
            currency="P"
          />
        )}
      </View>
    );
  };

  // Render beneficiaries tab
  const renderBeneficiariesTab = () => {
    return (
      <View>
        <Text style={styles.sectionTitle}>Beneficiaries</Text>
        <Text style={styles.sectionDescription}>
          Add beneficiaries who will receive the policy benefits. The total percentage must add up to 100%.
        </Text>

        <BeneficiaryManager
          beneficiaries={formData.beneficiaries}
          onBeneficiariesUpdated={handleBeneficiariesUpdated}
        />

        {errors.beneficiaries && (
          <Text style={[styles.errorText, { color: colors.error[500] }]}>
            {errors.beneficiaries}
          </Text>
        )}
      </View>
    );
  };

  return (
    <QuoteFormContainer
      title="Life Assurance Details"
      subtitle="Please provide details for your life assurance quote"
      steps={steps}
      currentStep={1}
      completedSteps={[0]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isQuoteLoading}
      nextDisabled={!formValid}
    >
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'details' && styles.activeTab
          ]}
          onPress={() => setActiveTab('details')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'details' && styles.activeTabText
          ]}>
            Coverage Details
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'beneficiaries' && styles.activeTab
          ]}
          onPress={() => setActiveTab('beneficiaries')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'beneficiaries' && styles.activeTabText
          ]}>
            Beneficiaries
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'documents' && styles.activeTab
          ]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'documents' && styles.activeTabText
          ]}>
            Required Documents
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        {/* Render components but only show the active one */}
        <View style={{ display: activeTab === 'details' ? 'flex' : 'none' }}>
          {renderFormFields()}
        </View>

        <View style={{ display: activeTab === 'beneficiaries' ? 'flex' : 'none' }}>
          {renderBeneficiariesTab()}
        </View>

        <View style={{ display: activeTab === 'documents' ? 'flex' : 'none' }}>
          <QuoteDocumentsSection
            quoteType="life"
            documents={documents}
            onDocumentsUpdated={handleDocumentsUpdated}
          />
        </View>
      </View>
    </QuoteFormContainer>
  );
}

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#6200ee',
  },
  tabText: {
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#6200ee',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  beneficiariesPlaceholder: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  placeholderText: {
    marginLeft: 8,
    color: '#666',
    flex: 1,
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
    marginBottom: 8,
  },
});
