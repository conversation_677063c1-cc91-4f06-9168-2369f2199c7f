import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { register, UserType } from '@/store/authSlice';
import { User, Mail, Phone, Lock, ArrowLeft, Building, Briefcase, CreditCard, UserCheck, FileText, MapPin } from 'lucide-react-native';
import Checkbox from 'expo-checkbox';

export default function RegisterScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  // Get auth state and actions from Redux store
  const dispatch = useAppDispatch();
  const { isLoading } = useAppSelector(state => state.auth);

  // User type selection
  const [userType, setUserType] = useState<UserType>('individual');

  // Common fields
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);

  // Individual user fields
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [idNumber, setIdNumber] = useState('');
  const [occupation, setOccupation] = useState('');

  // Business user fields
  const [companyName, setCompanyName] = useState('');
  const [registrationNumber, setRegistrationNumber] = useState('');
  const [contactPersonName, setContactPersonName] = useState('');
  const [industry, setIndustry] = useState('');
  const [taxNumber, setTaxNumber] = useState('');

  // Error states
  const [emailError, setEmailError] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [addressError, setAddressError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [termsError, setTermsError] = useState('');

  // Individual user error states
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [idNumberError, setIdNumberError] = useState('');

  // Business user error states
  const [companyNameError, setCompanyNameError] = useState('');
  const [registrationNumberError, setRegistrationNumberError] = useState('');
  const [contactPersonNameError, setContactPersonNameError] = useState('');

  // Validate first name
  const validateFirstName = () => {
    if (!firstName.trim()) {
      setFirstNameError('First name is required');
      return false;
    }
    setFirstNameError('');
    return true;
  };

  // Validate last name
  const validateLastName = () => {
    if (!lastName.trim()) {
      setLastNameError('Last name is required');
      return false;
    }
    setLastNameError('');
    return true;
  };

  // Validate email
  const validateEmail = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  // Validate phone
  const validatePhone = () => {
    const phoneRegex = /^\+?[0-9]{10,15}$/;
    if (!phone) {
      setPhoneError('Phone number is required');
      return false;
    } else if (!phoneRegex.test(phone)) {
      setPhoneError('Please enter a valid phone number');
      return false;
    }
    setPhoneError('');
    return true;
  };

  // Validate password
  const validatePassword = () => {
    if (!password) {
      setPasswordError('Password is required');
      return false;
    } else if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters');
      return false;
    } else if (!/[A-Z]/.test(password)) {
      setPasswordError('Password must contain at least one uppercase letter');
      return false;
    } else if (!/[0-9]/.test(password)) {
      setPasswordError('Password must contain at least one number');
      return false;
    }
    setPasswordError('');
    return true;
  };

  // Validate confirm password
  const validateConfirmPassword = () => {
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      return false;
    } else if (confirmPassword !== password) {
      setConfirmPasswordError('Passwords do not match');
      return false;
    }
    setConfirmPasswordError('');
    return true;
  };

  // Validate address
  const validateAddress = () => {
    if (address.trim() && address.trim().length < 5) {
      setAddressError('Address is too short');
      return false;
    }
    setAddressError('');
    return true;
  };

  // Validate ID number (for individual users)
  const validateIdNumber = () => {
    if (userType === 'individual' && idNumber.trim() && idNumber.trim().length < 5) {
      setIdNumberError('ID number is too short');
      return false;
    }
    setIdNumberError('');
    return true;
  };

  // Validate company name (for business users)
  const validateCompanyName = () => {
    if (userType === 'business' && !companyName.trim()) {
      setCompanyNameError('Company name is required');
      return false;
    }
    setCompanyNameError('');
    return true;
  };

  // Validate registration number (for business users)
  const validateRegistrationNumber = () => {
    if (userType === 'business' && !registrationNumber.trim()) {
      setRegistrationNumberError('Registration number is required');
      return false;
    }
    setRegistrationNumberError('');
    return true;
  };

  // Validate contact person name (for business users)
  const validateContactPersonName = () => {
    if (userType === 'business' && !contactPersonName.trim()) {
      setContactPersonNameError('Contact person name is required');
      return false;
    }
    setContactPersonNameError('');
    return true;
  };

  // Validate terms acceptance
  const validateTerms = () => {
    if (!acceptTerms) {
      setTermsError('You must accept the terms and conditions');
      return false;
    }
    setTermsError('');
    return true;
  };

  // Handle registration
  const handleRegister = async () => {
    console.log('Register button pressed');

    // Validate common inputs
    const isEmailValid = validateEmail();
    const isPhoneValid = validatePhone();
    const isAddressValid = validateAddress();
    const isPasswordValid = validatePassword();
    const isConfirmPasswordValid = validateConfirmPassword();
    const isTermsValid = validateTerms();

    // Validate user type specific inputs
    let isUserTypeFieldsValid = true;

    if (userType === 'individual') {
      const isFirstNameValid = validateFirstName();
      const isLastNameValid = validateLastName();
      const isIdNumberValid = validateIdNumber();

      isUserTypeFieldsValid = isFirstNameValid && isLastNameValid && isIdNumberValid;
    } else {
      const isCompanyNameValid = validateCompanyName();
      const isRegistrationNumberValid = validateRegistrationNumber();
      const isContactPersonNameValid = validateContactPersonName();

      isUserTypeFieldsValid = isCompanyNameValid && isRegistrationNumberValid && isContactPersonNameValid;
    }

    if (!isEmailValid || !isPhoneValid || !isAddressValid || !isPasswordValid ||
        !isConfirmPasswordValid || !isTermsValid || !isUserTypeFieldsValid) {
      console.log('Validation failed');
      return;
    }

    try {
      // Create registration data based on user type
      const commonData = {
        userType,
        email,
        phone,
        address,
        password,
        acceptTerms,
      };

      let registrationData;

      if (userType === 'individual') {
        registrationData = {
          ...commonData,
          firstName,
          lastName,
          idNumber,
          occupation,
        };
      } else {
        registrationData = {
          ...commonData,
          companyName,
          registrationNumber,
          contactPersonName,
          industry,
          taxNumber,
        };
      }

      const resultAction = await dispatch(register(registrationData));

      if (register.fulfilled.match(resultAction)) {
        console.log('Registration successful, navigating to OTP verification');
        router.push('/(auth)/verify-otp');
      } else if (register.rejected.match(resultAction)) {
        Alert.alert('Registration Failed', resultAction.payload as string || 'Failed to create account. Please try again.');
      }
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Registration Error', 'An unexpected error occurred. Please try again.');
    }
  };



  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContent: {
      flexGrow: 1,
    },
    contentContainer: {
      padding: spacing.lg,
    },
    header: {
      marginBottom: spacing.lg,
    },
    backButton: {
      marginBottom: spacing.md,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes['2xl'],
      color: colors.text,
      marginBottom: spacing.xs,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      marginBottom: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.md,
    },
    userTypeContainer: {
      marginBottom: spacing.md,
    },
    userTypeButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    userTypeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      width: '48%',
    },
    userTypeButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    userTypeButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.primary[500],
      marginLeft: spacing.sm,
    },
    userTypeButtonTextActive: {
      color: colors.white,
    },
    inputRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    inputHalf: {
      width: '48%',
    },
    inputContainer: {
      marginBottom: spacing.md,
    },
    inputLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      backgroundColor: colors.card,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      minHeight: 50,
    },
    input: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
      textAlignVertical: 'top',
    },
    errorText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.error[500],
      marginTop: spacing.xs,
    },
    termsContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: spacing.lg,
    },
    checkbox: {
      marginRight: spacing.sm,
      marginTop: 2,
    },
    termsText: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    termsLink: {
      fontFamily: typography.fonts.medium,
      color: colors.primary[500],
    },
    registerButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    registerButtonText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
    divider: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: spacing.lg,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: colors.border,
    },
    dividerText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginHorizontal: spacing.md,
    },
    googleButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      marginBottom: spacing.lg,
    },
    googleButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: spacing.lg,
      marginBottom: spacing.xl,
    },
    loginText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    loginLink: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.contentContainer}>
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color={colors.text} />
              </TouchableOpacity>
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>Sign up to get started</Text>
            </View>

            {/* User Type Selection */}
            <View style={styles.userTypeContainer}>
              <Text style={styles.sectionTitle}>Account Type</Text>
              <View style={styles.userTypeButtons}>
                <TouchableOpacity
                  style={[
                    styles.userTypeButton,
                    userType === 'individual' && styles.userTypeButtonActive
                  ]}
                  onPress={() => setUserType('individual')}
                >
                  <User size={20} color={userType === 'individual' ? colors.white : colors.primary[500]} />
                  <Text style={[
                    styles.userTypeButtonText,
                    userType === 'individual' && styles.userTypeButtonTextActive
                  ]}>Individual</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.userTypeButton,
                    userType === 'business' && styles.userTypeButtonActive
                  ]}
                  onPress={() => setUserType('business')}
                >
                  <Building size={20} color={userType === 'business' ? colors.white : colors.primary[500]} />
                  <Text style={[
                    styles.userTypeButtonText,
                    userType === 'business' && styles.userTypeButtonTextActive
                  ]}>Business</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Individual User Fields */}
            {userType === 'individual' && (
              <>
                <Text style={styles.sectionTitle}>Personal Information</Text>
                <View style={styles.inputRow}>
                  <View style={[styles.inputContainer, styles.inputHalf]}>
                    <Text style={styles.inputLabel}>First Name</Text>
                    <View style={[
                      styles.inputWrapper,
                      firstNameError ? { borderColor: colors.error[500] } : {}
                    ]}>
                      <User size={20} color={colors.textSecondary} />
                      <TextInput
                        style={styles.input}
                        placeholder="First name"
                        placeholderTextColor={colors.textSecondary}
                        value={firstName}
                        onChangeText={setFirstName}
                        onBlur={validateFirstName}
                      />
                    </View>
                    {firstNameError ? <Text style={styles.errorText}>{firstNameError}</Text> : null}
                  </View>

                  <View style={[styles.inputContainer, styles.inputHalf]}>
                    <Text style={styles.inputLabel}>Last Name</Text>
                    <View style={[
                      styles.inputWrapper,
                      lastNameError ? { borderColor: colors.error[500] } : {}
                    ]}>
                      <User size={20} color={colors.textSecondary} />
                      <TextInput
                        style={styles.input}
                        placeholder="Last name"
                        placeholderTextColor={colors.textSecondary}
                        value={lastName}
                        onChangeText={setLastName}
                        onBlur={validateLastName}
                      />
                    </View>
                    {lastNameError ? <Text style={styles.errorText}>{lastNameError}</Text> : null}
                  </View>
                </View>

                <View style={styles.inputRow}>
                  <View style={[styles.inputContainer, styles.inputHalf]}>
                    <Text style={styles.inputLabel}>ID Number</Text>
                    <View style={[
                      styles.inputWrapper,
                      idNumberError ? { borderColor: colors.error[500] } : {}
                    ]}>
                      <CreditCard size={20} color={colors.textSecondary} />
                      <TextInput
                        style={styles.input}
                        placeholder="ID/Passport number"
                        placeholderTextColor={colors.textSecondary}
                        value={idNumber}
                        onChangeText={setIdNumber}
                        onBlur={validateIdNumber}
                      />
                    </View>
                    {idNumberError ? <Text style={styles.errorText}>{idNumberError}</Text> : null}
                  </View>

                  <View style={[styles.inputContainer, styles.inputHalf]}>
                    <Text style={styles.inputLabel}>Occupation</Text>
                    <View style={styles.inputWrapper}>
                      <Briefcase size={20} color={colors.textSecondary} />
                      <TextInput
                        style={styles.input}
                        placeholder="Occupation (optional)"
                        placeholderTextColor={colors.textSecondary}
                        value={occupation}
                        onChangeText={setOccupation}
                      />
                    </View>
                  </View>
                </View>
              </>
            )}

            {/* Business User Fields */}
            {userType === 'business' && (
              <>
                <Text style={styles.sectionTitle}>Business Information</Text>
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Company Name</Text>
                  <View style={[
                    styles.inputWrapper,
                    companyNameError ? { borderColor: colors.error[500] } : {}
                  ]}>
                    <Building size={20} color={colors.textSecondary} />
                    <TextInput
                      style={styles.input}
                      placeholder="Company name"
                      placeholderTextColor={colors.textSecondary}
                      value={companyName}
                      onChangeText={setCompanyName}
                      onBlur={validateCompanyName}
                    />
                  </View>
                  {companyNameError ? <Text style={styles.errorText}>{companyNameError}</Text> : null}
                </View>

                <View style={styles.inputRow}>
                  <View style={[styles.inputContainer, styles.inputHalf]}>
                    <Text style={styles.inputLabel}>Registration Number</Text>
                    <View style={[
                      styles.inputWrapper,
                      registrationNumberError ? { borderColor: colors.error[500] } : {}
                    ]}>
                      <FileText size={20} color={colors.textSecondary} />
                      <TextInput
                        style={styles.input}
                        placeholder="Registration number"
                        placeholderTextColor={colors.textSecondary}
                        value={registrationNumber}
                        onChangeText={setRegistrationNumber}
                        onBlur={validateRegistrationNumber}
                      />
                    </View>
                    {registrationNumberError ? <Text style={styles.errorText}>{registrationNumberError}</Text> : null}
                  </View>

                  <View style={[styles.inputContainer, styles.inputHalf]}>
                    <Text style={styles.inputLabel}>Tax Number</Text>
                    <View style={styles.inputWrapper}>
                      <CreditCard size={20} color={colors.textSecondary} />
                      <TextInput
                        style={styles.input}
                        placeholder="Tax number (optional)"
                        placeholderTextColor={colors.textSecondary}
                        value={taxNumber}
                        onChangeText={setTaxNumber}
                      />
                    </View>
                  </View>
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Contact Person Name</Text>
                  <View style={[
                    styles.inputWrapper,
                    contactPersonNameError ? { borderColor: colors.error[500] } : {}
                  ]}>
                    <UserCheck size={20} color={colors.textSecondary} />
                    <TextInput
                      style={styles.input}
                      placeholder="Contact person name"
                      placeholderTextColor={colors.textSecondary}
                      value={contactPersonName}
                      onChangeText={setContactPersonName}
                      onBlur={validateContactPersonName}
                    />
                  </View>
                  {contactPersonNameError ? <Text style={styles.errorText}>{contactPersonNameError}</Text> : null}
                </View>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Industry</Text>
                  <View style={styles.inputWrapper}>
                    <Briefcase size={20} color={colors.textSecondary} />
                    <TextInput
                      style={styles.input}
                      placeholder="Industry (optional)"
                      placeholderTextColor={colors.textSecondary}
                      value={industry}
                      onChangeText={setIndustry}
                    />
                  </View>
                </View>
              </>
            )}

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <View style={[
                styles.inputWrapper,
                emailError ? { borderColor: colors.error[500] } : {}
              ]}>
                <Mail size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Email address"
                  placeholderTextColor={colors.textSecondary}
                  value={email}
                  onChangeText={setEmail}
                  onBlur={validateEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
              {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <View style={[
                styles.inputWrapper,
                phoneError ? { borderColor: colors.error[500] } : {}
              ]}>
                <Phone size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Phone number"
                  placeholderTextColor={colors.textSecondary}
                  value={phone}
                  onChangeText={setPhone}
                  onBlur={validatePhone}
                  keyboardType="phone-pad"
                />
              </View>
              {phoneError ? <Text style={styles.errorText}>{phoneError}</Text> : null}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Address</Text>
              <View style={[
                styles.inputWrapper,
                addressError ? { borderColor: colors.error[500] } : {}
              ]}>
                <MapPin size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Physical address"
                  placeholderTextColor={colors.textSecondary}
                  value={address}
                  onChangeText={setAddress}
                  onBlur={validateAddress}
                  multiline={true}
                  numberOfLines={2}
                />
              </View>
              {addressError ? <Text style={styles.errorText}>{addressError}</Text> : null}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Password</Text>
              <View style={[
                styles.inputWrapper,
                passwordError ? { borderColor: colors.error[500] } : {}
              ]}>
                <Lock size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Create password"
                  placeholderTextColor={colors.textSecondary}
                  value={password}
                  onChangeText={setPassword}
                  onBlur={validatePassword}
                  secureTextEntry
                />
              </View>
              {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Confirm Password</Text>
              <View style={[
                styles.inputWrapper,
                confirmPasswordError ? { borderColor: colors.error[500] } : {}
              ]}>
                <Lock size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Confirm password"
                  placeholderTextColor={colors.textSecondary}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  onBlur={validateConfirmPassword}
                  secureTextEntry
                />
              </View>
              {confirmPasswordError ? <Text style={styles.errorText}>{confirmPasswordError}</Text> : null}
            </View>

            <View style={styles.termsContainer}>
              <Checkbox
                style={styles.checkbox}
                value={acceptTerms}
                onValueChange={setAcceptTerms}
                color={acceptTerms ? colors.primary[500] : undefined}
              />
              <Text style={styles.termsText}>
                I agree to the{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => router.push('/legal/terms')}
                >
                  Terms and Conditions
                </Text>
                {' '}and{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => router.push('/legal/privacy')}
                >
                  Privacy Policy
                </Text>
              </Text>
            </View>
            {termsError ? <Text style={[styles.errorText, { marginTop: -spacing.md, marginBottom: spacing.md }]}>{termsError}</Text> : null}

            <TouchableOpacity
              style={styles.registerButton}
              onPress={handleRegister}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <Text style={styles.registerButtonText}>Create Account</Text>
              )}
            </TouchableOpacity>



            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>Already have an account?</Text>
              <TouchableOpacity onPress={() => router.push('/(auth)/login')}>
                <Text style={styles.loginLink}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
