module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Support for environment variables
      ['module:react-native-dotenv', {
        moduleName: '@env',
        path: '.env',
        blacklist: null,
        whitelist: null,
        safe: false,
        allowUndefined: true
      }],
      // Support for the new JSX transform
      ['@babel/plugin-transform-react-jsx', {
        runtime: 'automatic'
      }],
      // Support for decorators
      ['@babel/plugin-proposal-decorators', { legacy: true }],
      // Support for class properties
      ['@babel/plugin-proposal-class-properties', { loose: true }],
      // Support for the Reanimated plugin
      'react-native-reanimated/plugin',
      // Support for Expo Router
      'expo-router/babel'
    ]
  };
};
