/**
 * Document Types
 *
 * This file contains types and interfaces related to document management.
 */

// Document status types
export type DocumentStatus = 'pending' | 'verified' | 'rejected';

// Document category types
export type DocumentCategory =
  | 'ID'
  | 'Proof of Address'
  | 'Financial'
  | 'Insurance'
  | 'Policy'
  | 'Medical'
  | 'Vehicle'
  | 'Property'
  | 'Legal'
  | 'Payment'
  | 'Other';

// Document file types
export type DocumentFileType = 'image' | 'pdf' | 'doc' | 'excel' | 'powerpoint' | 'text' | 'rtf' | 'other';

// Base Document interface
export interface Document {
  id: string;
  name: string;
  type: DocumentCategory;
  status: DocumentStatus;
  date: string;
  reason?: string;
  uri?: string;
  fileName?: string;
  fileType?: DocumentFileType;
  fileUrl?: string;
  fileSize?: number;
  isImage?: boolean;
  metadata?: Record<string, any>;
  tags?: string[];
}

// Document with required verification
export interface VerificationDocument extends Document {
  requiredFor: string;
  expiryDate?: string;
  verifiedDate?: string;
  verifiedBy?: string;
}

// Policy document type
export type PolicyDocumentType =
  | 'policy_contract'
  | 'policy_schedule'
  | 'endorsement'
  | 'receipt'
  | 'claim_form'
  | 'claim_settlement'
  | 'renewal_notice'
  | 'premium_adjustment'
  | 'coverage_summary'
  | 'terms_and_conditions'
  | 'other';

// Policy document
export interface PolicyDocument extends Document {
  policyId: string;
  policyNumber: string;
  issueDate: string;
  expiryDate?: string;
  documentType: PolicyDocumentType;
  importance?: 'high' | 'medium' | 'low';
  relatedDocumentIds?: string[]; // IDs of related documents
}

// Empty arrays for documents - will be populated with user data
export const mockDocuments: Document[] = [];

// Empty arrays for verification documents
export const mockVerificationDocuments: VerificationDocument[] = [];

// Empty arrays for policy documents
export const mockPolicyDocuments: PolicyDocument[] = [];
