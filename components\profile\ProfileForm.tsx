import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { 
  UserProfileCreate, 
  UserProfileUpdate, 
  IDType,
  UserProfilePublic 
} from '@/types/backend';
import { createProfile, updateProfile, selectProfile } from '@/store/profileSlice';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import Button from '@/components/ui/Button';
import { User, Mail, Phone, MapPin, Calendar, CreditCard, Briefcase, Heart, Users } from 'lucide-react-native';

interface ProfileFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ onSuccess, onCancel }) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();
  
  const user = useAppSelector(state => state.auth.user);
  const existingProfile = useAppSelector(selectProfile);
  const isLoading = useAppSelector(state => state.profile.isLoading);
  
  const isEditing = !!existingProfile;

  // Form state
  const [formData, setFormData] = useState<UserProfileCreate>({
    first_name: existingProfile?.first_name || user?.firstName || '',
    last_name: existingProfile?.last_name || user?.lastName || '',
    id_type: existingProfile?.id_type || IDType.NATIONAL,
    id_number: existingProfile?.id_number || user?.idNumber || '',
    phone_number: existingProfile?.phone_number || user?.phone || '',
    address: existingProfile?.address || user?.address || '',
    date_of_birth: existingProfile?.date_of_birth || '',
    gender: existingProfile?.gender || '',
    occupation: existingProfile?.occupation || user?.occupation || '',
    employer: existingProfile?.employer || '',
    annual_income: existingProfile?.annual_income || undefined,
    marital_status: existingProfile?.marital_status || '',
    next_of_kin_name: existingProfile?.next_of_kin_name || '',
    next_of_kin_relationship: existingProfile?.next_of_kin_relationship || '',
    next_of_kin_phone: existingProfile?.next_of_kin_phone || '',
    emergency_contact_name: existingProfile?.emergency_contact_name || '',
    emergency_contact_phone: existingProfile?.emergency_contact_phone || '',
    emergency_contact_relationship: existingProfile?.emergency_contact_relationship || '',
    // Business fields (optional)
    business_name: existingProfile?.business_name || user?.userType === 'business' ? user?.companyName : '',
    business_registration_number: existingProfile?.business_registration_number || user?.userType === 'business' ? user?.registrationNumber : '',
    business_address: existingProfile?.business_address || '',
    business_phone: existingProfile?.business_phone || '',
    business_email: existingProfile?.business_email || '',
    business_type: existingProfile?.business_type || user?.userType === 'business' ? user?.industry : '',
    years_in_business: existingProfile?.years_in_business || undefined,
    annual_turnover: existingProfile?.annual_turnover || undefined,
    number_of_employees: existingProfile?.number_of_employees || undefined,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Options for select fields
  const idTypeOptions: SelectOption[] = [
    { value: IDType.NATIONAL, label: 'National ID' },
    { value: IDType.PASSPORT, label: 'Passport' },
  ];

  const genderOptions: SelectOption[] = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' },
    { value: 'prefer_not_to_say', label: 'Prefer not to say' },
  ];

  const maritalStatusOptions: SelectOption[] = [
    { value: 'single', label: 'Single' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' },
    { value: 'widowed', label: 'Widowed' },
    { value: 'separated', label: 'Separated' },
  ];

  const relationshipOptions: SelectOption[] = [
    { value: 'spouse', label: 'Spouse' },
    { value: 'parent', label: 'Parent' },
    { value: 'child', label: 'Child' },
    { value: 'sibling', label: 'Sibling' },
    { value: 'friend', label: 'Friend' },
    { value: 'colleague', label: 'Colleague' },
    { value: 'other', label: 'Other' },
  ];

  const updateFormField = (field: keyof UserProfileCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.first_name.trim()) newErrors.first_name = 'First name is required';
    if (!formData.last_name.trim()) newErrors.last_name = 'Last name is required';
    if (!formData.id_number.trim()) newErrors.id_number = 'ID number is required';
    if (!formData.phone_number.trim()) newErrors.phone_number = 'Phone number is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.date_of_birth) newErrors.date_of_birth = 'Date of birth is required';

    // Validate phone number format
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,15}$/;
    if (formData.phone_number && !phoneRegex.test(formData.phone_number)) {
      newErrors.phone_number = 'Please enter a valid phone number';
    }

    // Validate ID number format (basic validation)
    if (formData.id_number && formData.id_number.length < 5) {
      newErrors.id_number = 'ID number must be at least 5 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors in the form');
      return;
    }

    try {
      let result;
      if (isEditing) {
        // Update existing profile
        const updateData: UserProfileUpdate = { ...formData };
        result = await dispatch(updateProfile(updateData));
      } else {
        // Create new profile
        result = await dispatch(createProfile(formData));
      }

      if (result.meta.requestStatus === 'fulfilled') {
        Alert.alert(
          'Success',
          `Profile ${isEditing ? 'updated' : 'created'} successfully`,
          [{ text: 'OK', onPress: onSuccess }]
        );
      } else {
        Alert.alert('Error', result.payload as string || 'Failed to save profile');
      }
    } catch (error) {
      console.error('Profile save error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: spacing.md,
      marginTop: spacing.xl,
      marginBottom: spacing.xl,
    },
    button: {
      flex: 1,
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Personal Information */}
        <Text style={styles.sectionTitle}>Personal Information</Text>
        
        <DynamicFormField
          label="First Name"
          type="text"
          value={formData.first_name}
          onChange={(value) => updateFormField('first_name', value)}
          placeholder="Enter your first name"
          error={errors.first_name}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Last Name"
          type="text"
          value={formData.last_name}
          onChange={(value) => updateFormField('last_name', value)}
          placeholder="Enter your last name"
          error={errors.last_name}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="ID Type"
          type="select"
          value={formData.id_type}
          onChange={(value) => updateFormField('id_type', value)}
          options={idTypeOptions}
          icon={<CreditCard size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="ID Number"
          type="text"
          value={formData.id_number}
          onChange={(value) => updateFormField('id_number', value)}
          placeholder="Enter your ID number"
          error={errors.id_number}
          required
          icon={<CreditCard size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.date_of_birth}
          onChange={(value) => updateFormField('date_of_birth', value)}
          placeholder="Select your date of birth"
          error={errors.date_of_birth}
          required
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Gender"
          type="select"
          value={formData.gender}
          onChange={(value) => updateFormField('gender', value)}
          options={genderOptions}
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Marital Status"
          type="select"
          value={formData.marital_status}
          onChange={(value) => updateFormField('marital_status', value)}
          options={maritalStatusOptions}
          icon={<Heart size={20} color={colors.textSecondary} />}
        />

        {/* Contact Information */}
        <Text style={styles.sectionTitle}>Contact Information</Text>
        
        <DynamicFormField
          label="Phone Number"
          type="phone"
          value={formData.phone_number}
          onChange={(value) => updateFormField('phone_number', value)}
          placeholder="Enter your phone number"
          error={errors.phone_number}
          required
          icon={<Phone size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Address"
          type="textarea"
          value={formData.address}
          onChange={(value) => updateFormField('address', value)}
          placeholder="Enter your full address"
          error={errors.address}
          required
          icon={<MapPin size={20} color={colors.textSecondary} />}
        />

        {/* Employment Information */}
        <Text style={styles.sectionTitle}>Employment Information</Text>
        
        <DynamicFormField
          label="Occupation"
          type="text"
          value={formData.occupation}
          onChange={(value) => updateFormField('occupation', value)}
          placeholder="Enter your occupation"
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Employer"
          type="text"
          value={formData.employer}
          onChange={(value) => updateFormField('employer', value)}
          placeholder="Enter your employer name"
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Annual Income"
          type="number"
          value={formData.annual_income?.toString() || ''}
          onChange={(value) => updateFormField('annual_income', value ? parseFloat(value) : undefined)}
          placeholder="Enter your annual income"
          keyboardType="numeric"
        />

        {/* Emergency Contacts */}
        <Text style={styles.sectionTitle}>Emergency Contacts</Text>
        
        <DynamicFormField
          label="Next of Kin Name"
          type="text"
          value={formData.next_of_kin_name}
          onChange={(value) => updateFormField('next_of_kin_name', value)}
          placeholder="Enter next of kin name"
          icon={<Users size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Next of Kin Relationship"
          type="select"
          value={formData.next_of_kin_relationship}
          onChange={(value) => updateFormField('next_of_kin_relationship', value)}
          options={relationshipOptions}
          icon={<Heart size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Next of Kin Phone"
          type="phone"
          value={formData.next_of_kin_phone}
          onChange={(value) => updateFormField('next_of_kin_phone', value)}
          placeholder="Enter next of kin phone number"
          icon={<Phone size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Emergency Contact Name"
          type="text"
          value={formData.emergency_contact_name}
          onChange={(value) => updateFormField('emergency_contact_name', value)}
          placeholder="Enter emergency contact name"
          icon={<Users size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Emergency Contact Phone"
          type="phone"
          value={formData.emergency_contact_phone}
          onChange={(value) => updateFormField('emergency_contact_phone', value)}
          placeholder="Enter emergency contact phone"
          icon={<Phone size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Emergency Contact Relationship"
          type="select"
          value={formData.emergency_contact_relationship}
          onChange={(value) => updateFormField('emergency_contact_relationship', value)}
          options={relationshipOptions}
          icon={<Heart size={20} color={colors.textSecondary} />}
        />

        {/* Business Information (if applicable) */}
        {user?.userType === 'business' && (
          <>
            <Text style={styles.sectionTitle}>Business Information</Text>
            
            <DynamicFormField
              label="Business Name"
              type="text"
              value={formData.business_name}
              onChange={(value) => updateFormField('business_name', value)}
              placeholder="Enter business name"
              icon={<Briefcase size={20} color={colors.textSecondary} />}
            />

            <DynamicFormField
              label="Business Registration Number"
              type="text"
              value={formData.business_registration_number}
              onChange={(value) => updateFormField('business_registration_number', value)}
              placeholder="Enter registration number"
              icon={<CreditCard size={20} color={colors.textSecondary} />}
            />

            <DynamicFormField
              label="Business Address"
              type="textarea"
              value={formData.business_address}
              onChange={(value) => updateFormField('business_address', value)}
              placeholder="Enter business address"
              icon={<MapPin size={20} color={colors.textSecondary} />}
            />

            <DynamicFormField
              label="Business Phone"
              type="phone"
              value={formData.business_phone}
              onChange={(value) => updateFormField('business_phone', value)}
              placeholder="Enter business phone"
              icon={<Phone size={20} color={colors.textSecondary} />}
            />

            <DynamicFormField
              label="Business Email"
              type="email"
              value={formData.business_email}
              onChange={(value) => updateFormField('business_email', value)}
              placeholder="Enter business email"
              icon={<Mail size={20} color={colors.textSecondary} />}
            />

            <DynamicFormField
              label="Business Type"
              type="text"
              value={formData.business_type}
              onChange={(value) => updateFormField('business_type', value)}
              placeholder="Enter business type/industry"
              icon={<Briefcase size={20} color={colors.textSecondary} />}
            />

            <DynamicFormField
              label="Years in Business"
              type="number"
              value={formData.years_in_business?.toString() || ''}
              onChange={(value) => updateFormField('years_in_business', value ? parseInt(value) : undefined)}
              placeholder="Enter years in business"
              keyboardType="numeric"
            />

            <DynamicFormField
              label="Annual Turnover"
              type="number"
              value={formData.annual_turnover?.toString() || ''}
              onChange={(value) => updateFormField('annual_turnover', value ? parseFloat(value) : undefined)}
              placeholder="Enter annual turnover"
              keyboardType="numeric"
            />

            <DynamicFormField
              label="Number of Employees"
              type="number"
              value={formData.number_of_employees?.toString() || ''}
              onChange={(value) => updateFormField('number_of_employees', value ? parseInt(value) : undefined)}
              placeholder="Enter number of employees"
              keyboardType="numeric"
            />
          </>
        )}

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          {onCancel && (
            <Button
              title="Cancel"
              variant="outline"
              size="medium"
              onPress={onCancel}
              style={styles.button}
            />
          )}
          <Button
            title={isLoading ? 'Saving...' : (isEditing ? 'Update Profile' : 'Create Profile')}
            variant="primary"
            size="medium"
            onPress={handleSubmit}
            disabled={isLoading}
            style={styles.button}
            icon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ProfileForm;
