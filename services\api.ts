import axios, { AxiosResponse, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';

// Get API base URL from environment variables or use default
const API_BASE_URL = process.env.API_BASE_URL || 'https://inerca-backend.fly.dev/';

console.log('Using API base URL:', API_BASE_URL);

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor for adding token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    } catch (error) {
      console.error('Error in request interceptor:', error);
      return config;
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors - simplified for frontend-only operation
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error: AxiosError) => {
    // For frontend-only operation, just log the error and reject
    // No token refresh attempts since there's no backend
    console.log('API call failed (expected in frontend-only mode):', error.message);

    // Don't show error toasts for expected API failures in frontend-only mode
    // Only show toasts for unexpected errors
    if (error.code !== 'NETWORK_ERROR' && error.code !== 'ECONNREFUSED') {
      const responseData = error.response?.data as any;
      const errorMessage = responseData?.detail ||
                           responseData?.message ||
                           error.message ||
                           'An error occurred';

      showToast(
        'error',
        'Error',
        typeof errorMessage === 'string' ? errorMessage : 'An error occurred',
        { visibilityTime: 4000 }
      );
    }

    return Promise.reject(error);
  }
);

// API service functions
export const apiService = {
  // Auth endpoints
  auth: {
    login: async (email: string, password: string) => {
      const formData = new URLSearchParams();
      formData.append('username', email);
      formData.append('password', password);

      const response = await api.post('/api/v1/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      return response.data;
    },

    register: async (userData: any) => {
      const response = await api.post('/api/v1/register', userData);
      return response.data;
    },

    googleLogin: async () => {
      const response = await api.get('/api/v1/google/login');
      return response.data;
    },

    refreshToken: async () => {
      const response = await api.get('/api/v1/refresh-token');
      return response.data;
    },

    getCurrentUser: async () => {
      const response = await api.get('/api/v1/user/me');
      return response.data;
    },

    resetPassword: async (email: string, newPassword: string) => {
      const response = await api.patch(`/api/v1/reset-password/${email}`, {
        hashed_password: newPassword
      });
      return response.data;
    },

    updateAccount: async (userData: any) => {
      const response = await api.patch('/api/v1/update-account', userData);
      return response.data;
    },
  },

  // User profile endpoints
  userProfile: {
    getProfile: async () => {
      const response = await api.get('/api/v1/user-profile');
      return response.data;
    },

    createProfile: async (profileData: any) => {
      const response = await api.post('/api/v1/user-profile', profileData);
      return response.data;
    },

    updateProfile: async (profileData: any) => {
      const response = await api.patch('/api/v1/user-profile', profileData);
      return response.data;
    },
  },

  // Document endpoints
  documents: {
    uploadDocument: async (file: any, policyId?: string) => {
      const formData = new FormData();
      formData.append('file', file);

      let url = '/api/v1/documents';
      if (policyId) {
        url += `?policy_id=${policyId}`;
      }

      const response = await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },

    getAllDocuments: async () => {
      const response = await api.get('/api/v1/documents');
      return response.data;
    },

    downloadDocument: async (id: string) => {
      const response = await api.get(`/api/v1/documents/${id}/download`, {
        responseType: 'blob',
      });
      return response.data;
    },

    viewDocument: async (id: string) => {
      const response = await api.get(`/api/v1/documents/${id}/view`, {
        responseType: 'blob',
      });
      return response.data;
    },

    getPolicyDocuments: async (policyId: string) => {
      const response = await api.get(`/api/v1/documents/${policyId}/policy`);
      return response.data;
    },

    deleteDocument: async (id: string) => {
      const response = await api.delete(`/api/v1/documents/${id}/delete`);
      return response.data;
    },
  },

  // Policy endpoints
  policies: {
    createPolicy: async (policyData: any) => {
      const response = await api.post('/api/v1/policies', policyData);
      return response.data;
    },

    getUserPolicies: async (filters?: {
      policy_status?: string,
      policy_provider?: string,
      policy_type?: string
    }) => {
      const response = await api.get('/api/v1/policies', { params: filters });
      return response.data;
    },

    getPolicyById: async (id: string) => {
      const response = await api.get(`/api/v1/policies/${id}`);
      return response.data;
    },

    updatePolicy: async (id: string, policyData: any) => {
      const response = await api.patch(`/api/v1/policies/${id}`, policyData);
      return response.data;
    },

    renewPolicy: async (id: string) => {
      const response = await api.post(`/api/v1/policies/${id}/renew`);
      return response.data;
    },
  },

  // Claims endpoints
  claims: {
    getClaims: async (filters?: {
      status?: string,
      type?: string,
      policy_id?: string
    }) => {
      const response = await api.get('/api/v1/claims', { params: filters });
      return response.data;
    },

    getClaimById: async (id: string) => {
      const response = await api.get(`/api/v1/claims/${id}`);
      return response.data;
    },

    createClaim: async (claimData: any) => {
      const response = await api.post('/api/v1/claims', claimData);
      return response.data;
    },

    updateClaim: async (id: string, claimData: any) => {
      const response = await api.patch(`/api/v1/claims/${id}`, claimData);
      return response.data;
    },

    uploadDocument: async (claimId: string, file: any, documentType: string) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentType);

      const response = await api.post(`/api/v1/claims/${claimId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },

    getClaimDocuments: async (claimId: string) => {
      const response = await api.get(`/api/v1/claims/${claimId}/documents`);
      return response.data;
    },

    submitClaim: async (claimId: string) => {
      const response = await api.post(`/api/v1/claims/${claimId}/submit`);
      return response.data;
    },

    withdrawClaim: async (claimId: string, reason: string) => {
      const response = await api.post(`/api/v1/claims/${claimId}/withdraw`, { reason });
      return response.data;
    },

    getClaimTimeline: async (claimId: string) => {
      const response = await api.get(`/api/v1/claims/${claimId}/timeline`);
      return response.data;
    },
  },

  // Payment endpoints - simplified for document upload only
  payments: {
    uploadPaymentProof: async (applicationId: string, proofData: any) => {
      const response = await api.post(`/api/v1/payments/applications/${applicationId}/proof`, proofData);
      return response.data;
    },

    getPaymentStatus: async (applicationId: string) => {
      const response = await api.get(`/api/v1/payments/applications/${applicationId}/status`);
      return response.data;
    },

    getPaymentHistory: async (filters?: any) => {
      const response = await api.get('/api/v1/payments/history', { params: filters });
      return response.data;
    },

    getPaymentById: async (paymentId: string) => {
      const response = await api.get(`/api/v1/payments/${paymentId}`);
      return response.data;
    },

    verifyPayment: async (paymentId: string, verificationData: any) => {
      const response = await api.post(`/api/v1/payments/${paymentId}/verify`, verificationData);
      return response.data;
    },
  },

  // Application endpoints
  applications: {
    getApplications: async (filters?: {
      status?: string,
      type?: string
    }) => {
      const response = await api.get('/api/v1/applications', { params: filters });
      return response.data;
    },

    getApplicationById: async (id: string) => {
      const response = await api.get(`/api/v1/applications/${id}`);
      return response.data;
    },

    createApplication: async (applicationData: any) => {
      const response = await api.post('/api/v1/applications', applicationData);
      return response.data;
    },

    updateApplication: async (id: string, applicationData: any) => {
      const response = await api.patch(`/api/v1/applications/${id}`, applicationData);
      return response.data;
    },

    uploadApplicationDocument: async (applicationId: string, file: any, documentType: string) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentType);

      const response = await api.post(`/api/v1/applications/${applicationId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },
  },

  // Notification endpoints
  notifications: {
    getNotifications: async () => {
      const response = await api.get('/api/v1/notifications');
      return response.data;
    },

    markAsRead: async (id: string) => {
      const response = await api.patch(`/api/v1/notifications/${id}/read`);
      return response.data;
    },

    markAllAsRead: async () => {
      const response = await api.patch('/api/v1/notifications/read-all');
      return response.data;
    },

    deleteNotification: async (id: string) => {
      const response = await api.delete(`/api/v1/notifications/${id}`);
      return response.data;
    },
  },

  // Health check
  health: {
    check: async () => {
      const response = await api.get('/api/v1/health');
      return response.data;
    },
  },
};

export default apiService;
