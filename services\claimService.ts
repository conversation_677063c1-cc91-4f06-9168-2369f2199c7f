/**
 * Claim Service
 *
 * This service handles API interactions for claims management.
 * Provides both API and mock data implementations.
 */

import { Claim, ClaimInput, ClaimStatus, ClaimTimelineEvent, ClaimType, REQUIRED_DOCUMENTS_BY_CLAIM_TYPE } from '@/types/claim.types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService from './api';
import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import { showToast } from '@/utils/toast';

// Flag to determine if we should use mock data or real API
// Set to false when API is ready
const USE_MOCK_DATA = false;

// Mock claims data
let mockClaims: Claim[] = [];

// Helper function to generate a reference number
const generateReference = (): string => {
  return `CLM-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;
};

// Helper function to create initial timeline events
const createInitialTimeline = (claimType: ClaimType): ClaimTimelineEvent[] => {
  const currentDate = new Date().toISOString().split('T')[0];

  return [
    {
      id: '1',
      date: currentDate,
      title: 'Claim Created',
      description: 'You have started a new claim.',
      status: 'completed',
      icon: 'file-plus'
    },
    {
      id: '2',
      date: '',
      title: 'Claim Submission',
      description: 'Submit your claim with all required documents.',
      status: 'current',
      icon: 'upload',
      actions: [
        {
          label: 'Complete Submission',
          action: 'submit_claim'
        }
      ]
    },
    {
      id: '3',
      date: '',
      title: 'Claim Review',
      description: 'Your claim will be reviewed by our team.',
      status: 'upcoming',
      icon: 'clipboard'
    },
    {
      id: '4',
      date: '',
      title: 'Assessment',
      description: 'An assessor may be assigned to evaluate your claim.',
      status: 'upcoming',
      icon: 'search'
    },
    {
      id: '5',
      date: '',
      title: 'Decision',
      description: 'A decision will be made on your claim.',
      status: 'upcoming',
      icon: 'check-circle'
    },
    {
      id: '6',
      date: '',
      title: 'Payment',
      description: 'If approved, payment will be processed.',
      status: 'upcoming',
      icon: 'credit-card'
    }
  ];
};

// Helper function to create required documents
const createRequiredDocuments = (claimType: ClaimType) => {
  const currentDate = new Date().toISOString().split('T')[0];
  const requiredDocs = REQUIRED_DOCUMENTS_BY_CLAIM_TYPE[claimType] || [];

  return requiredDocs.map((doc, index) => ({
    id: `doc-${Date.now()}-${index}`,
    name: doc.name,
    type: doc.type,
    status: 'pending' as const,
    date: currentDate,
    required: doc.required
  }));
};

// Helper function to load claims from storage
const loadClaimsFromStorage = async (): Promise<Claim[]> => {
  try {
    const claimsJson = await AsyncStorage.getItem('claims');
    if (claimsJson) {
      return JSON.parse(claimsJson);
    }
    return [];
  } catch (error) {
    console.error('Error loading claims from storage:', error);
    return [];
  }
};

// Helper function to save claims to storage
const saveClaimsToStorage = async (claims: Claim[]): Promise<void> => {
  try {
    await AsyncStorage.setItem('claims', JSON.stringify(claims));
  } catch (error) {
    console.error('Error saving claims to storage:', error);
  }
};

// Initialize claims from storage
const initializeClaims = async () => {
  mockClaims = await loadClaimsFromStorage();
};

// Initialize on module load
initializeClaims();

// Claim service methods
export const claimService = {
  // Get all claims
  getAllClaims: async (): Promise<Claim[]> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Return mock claims
      return mockClaims;
    } else {
      try {
        const response = await apiService.claims.getClaims();
        return response;
      } catch (error) {
        console.error('Error getting claims:', error);

        // Fallback to mock data if API fails
        console.log('Falling back to mock data for claims');
        return mockClaims;
      }
    }
  },

  // Get claim by ID
  getClaimById: async (id: string): Promise<Claim | undefined> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Find claim by ID
      return mockClaims.find(claim => claim.id === id);
    } else {
      try {
        const response = await apiService.claims.getClaimById(id);
        return response;
      } catch (error) {
        console.error(`Error getting claim with ID ${id}:`, error);

        // Fallback to mock data if API fails
        console.log('Falling back to mock data for claim details');
        return mockClaims.find(claim => claim.id === id);
      }
    }
  },

  // Create a new claim
  createClaim: async (claimInput: ClaimInput): Promise<Claim> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      const currentDate = new Date().toISOString().split('T')[0];

      // Create new claim
      const newClaim: Claim = {
        id: Date.now().toString(),
        ...claimInput,
        status: 'draft',
        reference: generateReference(),
        date: currentDate,
        documents: createRequiredDocuments(claimInput.type),
        timeline: createInitialTimeline(claimInput.type),
        notes: [],
        requiredActions: [
          {
            id: `action-${Math.random().toString(36).substring(2, 9)}`,
            description: 'Complete claim form and upload required documents',
            priority: 'high',
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            completed: false
          }
        ]
      };

      // Add to mock claims
      mockClaims = [newClaim, ...mockClaims];

      // Save to storage
      await saveClaimsToStorage(mockClaims);

      return newClaim;
    } else {
      try {
        const response = await apiService.claims.createClaim(claimInput);
        return response;
      } catch (error) {
        console.error('Error creating claim:', error);
        throw error;
      }
    }
  },

  // Update claim
  updateClaim: async (id: string, updates: Partial<Claim>): Promise<Claim> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Find claim index
      const claimIndex = mockClaims.findIndex(claim => claim.id === id);

      if (claimIndex === -1) {
        throw new Error(`Claim with ID ${id} not found`);
      }

      // Update claim
      const updatedClaim = {
        ...mockClaims[claimIndex],
        ...updates
      };

      // Update in mock claims
      mockClaims[claimIndex] = updatedClaim;

      // Save to storage
      await saveClaimsToStorage(mockClaims);

      return updatedClaim;
    } else {
      try {
        const response = await apiService.claims.updateClaim(id, updates);
        return response;
      } catch (error) {
        console.error(`Error updating claim with ID ${id}:`, error);
        throw error;
      }
    }
  },

  // Submit claim
  submitClaim: async (id: string): Promise<Claim> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Find claim index
      const claimIndex = mockClaims.findIndex(claim => claim.id === id);

      if (claimIndex === -1) {
        throw new Error(`Claim with ID ${id} not found`);
      }

      const currentDate = new Date().toISOString().split('T')[0];

      // Update timeline
      const updatedTimeline = mockClaims[claimIndex].timeline.map(event => {
        if (event.title === 'Claim Submission') {
          return { ...event, status: 'completed', date: currentDate };
        } else if (event.title === 'Claim Review') {
          return { ...event, status: 'current', date: currentDate };
        }
        return event;
      });

      // Update claim
      const updatedClaim = {
        ...mockClaims[claimIndex],
        status: 'submitted' as ClaimStatus,
        timeline: updatedTimeline,
        requiredActions: mockClaims[claimIndex].requiredActions?.map(action => {
          if (action.description.includes('Complete claim form')) {
            return { ...action, completed: true };
          }
          return action;
        })
      };

      // Update in mock claims
      mockClaims[claimIndex] = updatedClaim;

      // Save to storage
      await saveClaimsToStorage(mockClaims);

      // Simulate claim review process (after 15 seconds)
      setTimeout(() => {
        claimService.updateClaimStatus(id, 'under_review');
      }, 15000);

      return updatedClaim;
    } else {
      try {
        // In a real API, this would be a specific endpoint for submitting a claim
        const response = await apiService.claims.updateClaim(id, { status: 'submitted' });
        return response;
      } catch (error) {
        console.error(`Error submitting claim with ID ${id}:`, error);
        throw error;
      }
    }
  },

  // Update claim status
  updateClaimStatus: async (id: string, status: ClaimStatus): Promise<Claim> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Find claim index
      const claimIndex = mockClaims.findIndex(claim => claim.id === id);

      if (claimIndex === -1) {
        throw new Error(`Claim with ID ${id} not found`);
      }

      const currentDate = new Date().toISOString().split('T')[0];

      // Update timeline based on new status
      let updatedTimeline = [...mockClaims[claimIndex].timeline];

      switch (status) {
        case 'under_review':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Claim Review') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Assessment') {
              return { ...event, status: 'current', date: currentDate };
            }
            return event;
          });
          break;
        case 'approved':
        case 'partially_approved':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Assessment') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Decision') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Payment') {
              return { ...event, status: 'current', date: currentDate };
            }
            return event;
          });
          break;
        case 'rejected':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Assessment') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Decision') {
              return { ...event, status: 'completed', date: currentDate };
            }
            return event;
          });
          break;
        case 'paid':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Payment') {
              return { ...event, status: 'completed', date: currentDate };
            }
            return event;
          });
          break;
      }

      // Update claim
      const updatedClaim = {
        ...mockClaims[claimIndex],
        status,
        timeline: updatedTimeline
      };

      // Update in mock claims
      mockClaims[claimIndex] = updatedClaim;

      // Save to storage
      await saveClaimsToStorage(mockClaims);

      return updatedClaim;
    } else {
      try {
        const response = await apiService.claims.updateClaim(id, { status });
        return response;
      } catch (error) {
        console.error(`Error updating claim status for ID ${id}:`, error);
        throw error;
      }
    }
  },

  // Upload a document for a claim
  uploadClaimDocument: async (claimId: string, file: any) => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find claim index
      const claimIndex = mockClaims.findIndex(claim => claim.id === claimId);

      if (claimIndex === -1) {
        throw new Error(`Claim with ID ${claimId} not found`);
      }

      const currentDate = new Date().toISOString().split('T')[0];

      // Create a mock document
      const newDocument = {
        id: `doc-${Date.now()}`,
        name: file.name || 'Document',
        type: file.type || 'Other',
        status: 'pending' as const,
        date: currentDate,
        required: true,
        documentId: `file-${Date.now()}`
      };

      // Update claim documents
      const updatedClaim = {
        ...mockClaims[claimIndex],
        documents: [...mockClaims[claimIndex].documents, newDocument]
      };

      // Update in mock claims
      mockClaims[claimIndex] = updatedClaim;

      // Save to storage
      await saveClaimsToStorage(mockClaims);

      // Simulate document verification (after 15 seconds)
      setTimeout(() => {
        const claimIndex = mockClaims.findIndex(claim => claim.id === claimId);
        if (claimIndex !== -1) {
          const docIndex = mockClaims[claimIndex].documents.findIndex(doc => doc.id === newDocument.id);
          if (docIndex !== -1) {
            mockClaims[claimIndex].documents[docIndex].status = 'verified';
            saveClaimsToStorage(mockClaims);
          }
        }
      }, 15000);

      return newDocument;
    } else {
      try {
        const response = await apiService.claims.uploadClaimDocument(claimId, file);
        return response;
      } catch (error) {
        console.error(`Error uploading document for claim with ID ${claimId}:`, error);
        throw error;
      }
    }
  },

  // Pick a document for a claim
  pickClaimDocument: async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf'],
        copyToCacheDirectory: true
      });

      if (result.canceled) {
        throw new Error('Document picking canceled');
      }

      const asset = result.assets?.[0];
      if (!asset) {
        throw new Error('No document selected');
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist');
      }

      // Check file size (limit to 10MB)
      if (fileInfo.size && fileInfo.size > 10 * 1024 * 1024) {
        showToast(
          'error',
          'File Too Large',
          'Please select a file smaller than 10MB',
          { visibilityTime: 3000 }
        );
        throw new Error('File too large');
      }

      return {
        uri: asset.uri,
        name: asset.name || 'claim_document.pdf',
        type: asset.mimeType || 'application/pdf',
        size: fileInfo.size
      };
    } catch (error) {
      console.error('Error picking claim document:', error);
      throw error;
    }
  }
};

export default claimService;
