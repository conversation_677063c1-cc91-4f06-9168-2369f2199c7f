import apiService from './api';
import { Notification } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Flag to determine if we should use mock data or real API
// Set to false when API is ready
const USE_MOCK_DATA = true;

// Mock notifications data
let mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Welcome to Inerca',
    message: 'Thank you for joining Inerca. We are here to help you with all your insurance needs.',
    type: 'info',
    read: false,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: '2',
    title: 'Complete Your Profile',
    message: 'Please complete your profile to get personalized insurance quotes.',
    type: 'info',
    read: false,
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
  }
];

// Helper function to load notifications from storage
const loadNotificationsFromStorage = async (): Promise<Notification[]> => {
  try {
    const notificationsJson = await AsyncStorage.getItem('notifications');
    if (notificationsJson) {
      return JSON.parse(notificationsJson);
    }
    return mockNotifications;
  } catch (error) {
    console.error('Error loading notifications from storage:', error);
    return mockNotifications;
  }
};

// Helper function to save notifications to storage
const saveNotificationsToStorage = async (notifications: Notification[]): Promise<void> => {
  try {
    await AsyncStorage.setItem('notifications', JSON.stringify(notifications));
  } catch (error) {
    console.error('Error saving notifications to storage:', error);
  }
};

// Initialize notifications from storage
const initializeNotifications = async () => {
  mockNotifications = await loadNotificationsFromStorage();
};

// Initialize on module load
initializeNotifications();

// Configure Expo Notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Register for push notifications
const registerForPushNotifications = async () => {
  if (!Device.isDevice) {
    console.log('Push notifications are not available in the simulator');
    return null;
  }

  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    const token = (await Notifications.getExpoPushTokenAsync()).data;
    console.log('Expo push token:', token);

    // Save token to AsyncStorage
    await AsyncStorage.setItem('pushToken', token);

    // Configure for Android
    if (Platform.OS === 'android') {
      Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    return token;
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return null;
  }
};

// Notification service methods
export const notificationService = {
  // Register for push notifications
  registerForPushNotifications,

  // Get all notifications
  getNotifications: async (): Promise<Notification[]> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Return mock notifications
      return mockNotifications;
    } else {
      try {
        const response = await apiService.notifications.getNotifications();
        return response;
      } catch (error) {
        console.error('Error getting notifications:', error);

        // Fallback to mock data if API fails
        console.log('Falling back to mock data for notifications');
        return mockNotifications;
      }
    }
  },

  // Mark notification as read
  markAsRead: async (id: string): Promise<void> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Find notification index
      const notificationIndex = mockNotifications.findIndex(notification => notification.id === id);

      if (notificationIndex === -1) {
        throw new Error(`Notification with ID ${id} not found`);
      }

      // Update notification
      mockNotifications[notificationIndex].read = true;

      // Save to storage
      await saveNotificationsToStorage(mockNotifications);
    } else {
      try {
        await apiService.notifications.markAsRead(id);
      } catch (error) {
        console.error(`Error marking notification with ID ${id} as read:`, error);
        throw error;
      }
    }
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update all notifications
      mockNotifications = mockNotifications.map(notification => ({
        ...notification,
        read: true
      }));

      // Save to storage
      await saveNotificationsToStorage(mockNotifications);
    } else {
      try {
        await apiService.notifications.markAllAsRead();
      } catch (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
      }
    }
  },

  // Delete notification
  deleteNotification: async (id: string): Promise<void> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Filter out notification
      mockNotifications = mockNotifications.filter(notification => notification.id !== id);

      // Save to storage
      await saveNotificationsToStorage(mockNotifications);
    } else {
      try {
        await apiService.notifications.deleteNotification(id);
      } catch (error) {
        console.error(`Error deleting notification with ID ${id}:`, error);
        throw error;
      }
    }
  },

  // Send local notification
  sendLocalNotification: async (title: string, body: string, data?: any): Promise<void> => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending local notification:', error);
      throw error;
    }
  },

  // Clear all notifications
  clearAllNotifications: async (): Promise<void> => {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Clear all notifications
      mockNotifications = [];

      // Save to storage
      await saveNotificationsToStorage(mockNotifications);
    } else {
      try {
        await apiService.notifications.clearAllNotifications();
      } catch (error) {
        console.error('Error clearing all notifications:', error);
        throw error;
      }
    }
  }
};

export default notificationService;
