import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';

// Define the types for legal documents
export type LegalDocumentType = 'terms' | 'privacy';

export interface LegalDocumentVersion {
  id: string;
  version: string;
  publishDate: string;
  content: string;
  isActive: boolean;
}

export interface UserAcceptance {
  userId: string;
  documentType: LegalDocumentType;
  versionId: string;
  acceptedAt: string;
}

// Define the store state
interface LegalState {
  termsVersions: LegalDocumentVersion[];
  privacyVersions: LegalDocumentVersion[];
  userAcceptances: UserAcceptance[];
  currentTermsVersion: LegalDocumentVersion | null;
  currentPrivacyVersion: LegalDocumentVersion | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchLegalDocuments: () => Promise<void>;
  getDocumentVersions: (type: LegalDocumentType) => LegalDocumentVersion[];
  getCurrentVersion: (type: LegalDocumentType) => LegalDocumentVersion | null;
  getVersionById: (type: LegalDocumentType, id: string) => LegalDocumentVersion | null;
  hasUserAccepted: (type: LegalDocumentType, userId: string) => boolean;
  acceptLegalDocument: (type: LegalDocumentType, userId: string) => Promise<void>;
  getUserAcceptanceHistory: (userId: string, type: LegalDocumentType) => UserAcceptance[];
  addNewVersion: (type: LegalDocumentType, version: Omit<LegalDocumentVersion, 'id'>) => Promise<void>;
  setActiveVersion: (type: LegalDocumentType, id: string) => Promise<void>;
  getAcceptanceRate: (type: LegalDocumentType, versionId: string) => number;
}

// Create the store
const useLegalStore = create<LegalState>((set, get) => ({
  termsVersions: [],
  privacyVersions: [],
  userAcceptances: [],
  currentTermsVersion: null,
  currentPrivacyVersion: null,
  isLoading: false,
  error: null,

  // Fetch legal documents
  fetchLegalDocuments: async () => {
    set({ isLoading: true, error: null });
    try {
      // Try to load from AsyncStorage first
      const termsData = await AsyncStorage.getItem('termsVersions');
      const privacyData = await AsyncStorage.getItem('privacyVersions');
      const acceptancesData = await AsyncStorage.getItem('legalAcceptances');

      let termsVersions: LegalDocumentVersion[] = [];
      let privacyVersions: LegalDocumentVersion[] = [];
      let userAcceptances: UserAcceptance[] = [];

      if (termsData) {
        termsVersions = JSON.parse(termsData);
      } else {
        // Initialize with default terms version if none exists
        termsVersions = [
          {
            id: 'terms-v1.0',
            version: '1.0',
            publishDate: '2025-04-16',
            content: 'Default Terms and Conditions content',
            isActive: true,
          },
        ];
        await AsyncStorage.setItem('termsVersions', JSON.stringify(termsVersions));
      }

      if (privacyData) {
        privacyVersions = JSON.parse(privacyData);
      } else {
        // Initialize with default privacy version if none exists
        privacyVersions = [
          {
            id: 'privacy-v1.0',
            version: '1.0',
            publishDate: '2025-04-16',
            content: 'Default Privacy Policy content',
            isActive: true,
          },
        ];
        await AsyncStorage.setItem('privacyVersions', JSON.stringify(privacyVersions));
      }

      if (acceptancesData) {
        userAcceptances = JSON.parse(acceptancesData);
      }

      // Find current active versions
      const currentTermsVersion = termsVersions.find(v => v.isActive) || null;
      const currentPrivacyVersion = privacyVersions.find(v => v.isActive) || null;

      set({
        termsVersions,
        privacyVersions,
        userAcceptances,
        currentTermsVersion,
        currentPrivacyVersion,
        isLoading: false,
      });
    } catch (error) {
      console.error('Error fetching legal documents:', error);
      set({
        error: 'Failed to fetch legal documents. Please try again.',
        isLoading: false,
      });
    }
  },

  // Get all versions of a document type
  getDocumentVersions: (type: LegalDocumentType) => {
    return type === 'terms' ? get().termsVersions : get().privacyVersions;
  },

  // Get current active version of a document type
  getCurrentVersion: (type: LegalDocumentType) => {
    return type === 'terms' ? get().currentTermsVersion : get().currentPrivacyVersion;
  },

  // Get a specific version by ID
  getVersionById: (type: LegalDocumentType, id: string) => {
    const versions = type === 'terms' ? get().termsVersions : get().privacyVersions;
    return versions.find(v => v.id === id) || null;
  },

  // Check if user has accepted the current version
  hasUserAccepted: (type: LegalDocumentType, userId: string) => {
    const currentVersion = type === 'terms' ? get().currentTermsVersion : get().currentPrivacyVersion;
    if (!currentVersion) return false;

    return get().userAcceptances.some(
      acceptance =>
        acceptance.userId === userId &&
        acceptance.documentType === type &&
        acceptance.versionId === currentVersion.id
    );
  },

  // Accept a legal document
  acceptLegalDocument: async (type: LegalDocumentType, userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const currentVersion = type === 'terms' ? get().currentTermsVersion : get().currentPrivacyVersion;

      if (!currentVersion) {
        throw new Error(`No active ${type} version found`);
      }

      // Create new acceptance record
      const newAcceptance: UserAcceptance = {
        userId,
        documentType: type,
        versionId: currentVersion.id,
        acceptedAt: new Date().toISOString(),
      };

      // Update state
      const updatedAcceptances = [...get().userAcceptances, newAcceptance];

      // Save to AsyncStorage
      await AsyncStorage.setItem('legalAcceptances', JSON.stringify(updatedAcceptances));

      set({
        userAcceptances: updatedAcceptances,
        isLoading: false,
      });

      showToast(
        'success',
        `${type === 'terms' ? 'Terms & Conditions' : 'Privacy Policy'} Accepted`,
        'Thank you for accepting our legal terms.',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error(`Error accepting ${type}:`, error);
      set({
        error: `Failed to accept ${type}. Please try again.`,
        isLoading: false,
      });
      throw error;
    }
  },

  // Get user's acceptance history for a document type
  getUserAcceptanceHistory: (userId: string, type: LegalDocumentType) => {
    return get().userAcceptances.filter(
      acceptance => acceptance.userId === userId && acceptance.documentType === type
    );
  },

  // Add a new version of a legal document
  addNewVersion: async (type: LegalDocumentType, version: Omit<LegalDocumentVersion, 'id'>) => {
    set({ isLoading: true, error: null });
    try {
      // Generate a unique ID for the new version
      const id = `${type}-v${version.version}-${Date.now()}`;

      // Create the new version object
      const newVersion: LegalDocumentVersion = {
        ...version,
        id,
      };

      // Get current versions
      const currentVersions = type === 'terms' ? get().termsVersions : get().privacyVersions;

      // If this is marked as active, deactivate all other versions
      let updatedVersions: LegalDocumentVersion[];

      if (version.isActive) {
        updatedVersions = currentVersions.map(v => ({
          ...v,
          isActive: false, // Deactivate all existing versions
        }));

        // Add the new active version
        updatedVersions.push(newVersion);

        // Update the current version reference
        if (type === 'terms') {
          set({ currentTermsVersion: newVersion });
        } else {
          set({ currentPrivacyVersion: newVersion });
        }
      } else {
        // Just add the new version without changing active status of others
        updatedVersions = [...currentVersions, newVersion];
      }

      // Update state
      if (type === 'terms') {
        set({ termsVersions: updatedVersions });
        await AsyncStorage.setItem('termsVersions', JSON.stringify(updatedVersions));
      } else {
        set({ privacyVersions: updatedVersions });
        await AsyncStorage.setItem('privacyVersions', JSON.stringify(updatedVersions));
      }

      set({ isLoading: false });

      showToast(
        'success',
        `New ${type === 'terms' ? 'Terms & Conditions' : 'Privacy Policy'} Version Added`,
        `Version ${version.version} has been added successfully.`,
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error(`Error adding new ${type} version:`, error);
      set({
        error: `Failed to add new ${type} version. Please try again.`,
        isLoading: false,
      });
      throw error;
    }
  },

  // Set a version as the active version
  setActiveVersion: async (type: LegalDocumentType, id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get current versions
      const currentVersions = type === 'terms' ? get().termsVersions : get().privacyVersions;

      // Find the version to activate
      const versionToActivate = currentVersions.find(v => v.id === id);

      if (!versionToActivate) {
        throw new Error(`Version with ID ${id} not found`);
      }

      // Update all versions (deactivate all, then activate the selected one)
      const updatedVersions = currentVersions.map(v => ({
        ...v,
        isActive: v.id === id,
      }));

      // Update state
      if (type === 'terms') {
        set({
          termsVersions: updatedVersions,
          currentTermsVersion: versionToActivate,
        });
        await AsyncStorage.setItem('termsVersions', JSON.stringify(updatedVersions));
      } else {
        set({
          privacyVersions: updatedVersions,
          currentPrivacyVersion: versionToActivate,
        });
        await AsyncStorage.setItem('privacyVersions', JSON.stringify(updatedVersions));
      }

      set({ isLoading: false });

      showToast(
        'success',
        `${type === 'terms' ? 'Terms & Conditions' : 'Privacy Policy'} Version Activated`,
        `Version ${versionToActivate.version} is now the active version.`,
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error(`Error setting active ${type} version:`, error);
      set({
        error: `Failed to set active ${type} version. Please try again.`,
        isLoading: false,
      });
      throw error;
    }
  },

  // Get acceptance rate for a specific version
  getAcceptanceRate: (type: LegalDocumentType, versionId: string) => {
    // Get all users who have accepted this version
    const acceptances = get().userAcceptances.filter(
      acceptance => acceptance.documentType === type && acceptance.versionId === versionId
    );

    // In a real app, you would compare this to the total number of active users
    // For demo purposes, we'll just return the raw count
    return acceptances.length;
  },
}));

export default useLegalStore;
