import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import notificationService from '@/services/notificationService';

// Define notification types
export type NotificationType = 'info' | 'warning' | 'error' | 'success' | 'policy' | 'claim' | 'application' | 'document' | 'payment' | 'renewal';

// Define notification interface
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt: string;
  actionRoute?: string;
  actionLabel?: string;
  actionData?: any;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  expiresAt?: string;
  userId?: string;
}

// Define notification preferences
export interface NotificationPreferences {
  enablePush: boolean;
  enableEmail: boolean;
  enableSMS: boolean;
  categories: {
    [key: string]: boolean;
  };
}

// Define notification store state
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  preferences: NotificationPreferences;

  // Actions
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAllNotifications: () => Promise<void>;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => Promise<void>;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
  getNotificationsByCategory: (category: string) => Notification[];
  getUnreadNotificationsByCategory: (category: string) => Notification[];
}

// Default notification preferences
const defaultPreferences: NotificationPreferences = {
  enablePush: true,
  enableEmail: true,
  enableSMS: false,
  categories: {
    policy: true,
    claim: true,
    payment: true,
    document: true,
    application: true,
    renewal: true,
    system: true,
  },
}

// Create the store
const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  preferences: defaultPreferences,

  // Fetch all notifications
  fetchNotifications: async () => {
    set({ isLoading: true, error: null });
    try {
      // Fetch notifications from service
      const notifications = await notificationService.getNotifications();

      // Calculate unread count
      const unreadCount = notifications.filter(notification => !notification.read).length;

      // Update state
      set({
        notifications,
        unreadCount,
        isLoading: false
      });

      // Load preferences
      try {
        const preferencesJson = await AsyncStorage.getItem('notificationPreferences');
        if (preferencesJson) {
          const preferences = JSON.parse(preferencesJson);
          set({ preferences });
        }
      } catch (error) {
        console.error('Error loading notification preferences:', error);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      set({
        error: 'Failed to fetch notifications. Please try again.',
        isLoading: false
      });
    }
  },

  // Mark notification as read
  markAsRead: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Mark notification as read in service
      await notificationService.markAsRead(id);

      // Update state
      set(state => {
        const updatedNotifications = state.notifications.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        );

        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(notification => !notification.read).length,
          isLoading: false
        };
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      set({
        error: 'Failed to mark notification as read. Please try again.',
        isLoading: false
      });
    }
  },

  // Mark all notifications as read
  markAllAsRead: async () => {
    set({ isLoading: true, error: null });
    try {
      // Mark all notifications as read in service
      await notificationService.markAllAsRead();

      // Update state
      set(state => ({
        notifications: state.notifications.map(notification => ({ ...notification, read: true })),
        unreadCount: 0,
        isLoading: false
      }));

      // Show success toast
      showToast(
        'success',
        'All Notifications Read',
        'All notifications have been marked as read',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      set({
        error: 'Failed to mark all notifications as read. Please try again.',
        isLoading: false
      });
    }
  },

  // Delete notification
  deleteNotification: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Delete notification in service
      await notificationService.deleteNotification(id);

      // Update state
      set(state => {
        const updatedNotifications = state.notifications.filter(notification => notification.id !== id);

        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(notification => !notification.read).length,
          isLoading: false
        };
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      set({
        error: 'Failed to delete notification. Please try again.',
        isLoading: false
      });
    }
  },

  // Clear all notifications
  clearAllNotifications: async () => {
    set({ isLoading: true, error: null });
    try {
      // Clear all notifications in service
      await notificationService.clearAllNotifications();

      // Update state
      set({
        notifications: [],
        unreadCount: 0,
        isLoading: false
      });
    } catch (error) {
      console.error('Error clearing notifications:', error);
      set({
        error: 'Failed to clear notifications. Please try again.',
        isLoading: false
      });
    }
  },

  // Add a new notification
  addNotification: async (notification: Omit<Notification, 'id' | 'createdAt'>) => {
    set({ isLoading: true, error: null });
    try {
      // Create new notification
      const newNotification: Notification = {
        ...notification,
        id: `notification-${Date.now()}`,
        createdAt: new Date().toISOString(),
      };

      // Update state
      set(state => {
        const updatedNotifications = [newNotification, ...state.notifications];

        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(notification => !notification.read).length,
          isLoading: false
        };
      });

      // Send local notification if push is enabled
      if (get().preferences.enablePush) {
        await notificationService.sendLocalNotification(
          notification.title,
          notification.message,
          notification.actionData
        );
      }
    } catch (error) {
      console.error('Error adding notification:', error);
      set({
        error: 'Failed to add notification. Please try again.',
        isLoading: false
      });
    }
  },

  // Update notification preferences
  updatePreferences: async (preferences: Partial<NotificationPreferences>) => {
    try {
      const updatedPreferences = {
        ...get().preferences,
        ...preferences
      };

      // Save to AsyncStorage
      await AsyncStorage.setItem('notificationPreferences', JSON.stringify(updatedPreferences));

      // Update state
      set({ preferences: updatedPreferences });

      // Show success toast
      showToast(
        'success',
        'Preferences Updated',
        'Your notification preferences have been updated',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      showToast(
        'error',
        'Update Failed',
        'Failed to update notification preferences',
        { visibilityTime: 2000 }
      );
    }
  },

  // Get notifications by category
  getNotificationsByCategory: (category: string) => {
    return get().notifications.filter(notification => notification.category === category);
  },

  // Get unread notifications by category
  getUnreadNotificationsByCategory: (category: string) => {
    return get().notifications.filter(
      notification => notification.category === category && !notification.read
    );
  },
}));

export default useNotificationStore;
