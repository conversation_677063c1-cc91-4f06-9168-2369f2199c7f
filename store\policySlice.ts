import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { 
  PolicyCreate, 
  PolicyUpdate, 
  PolicyPublic,
  PolicyType,
  PolicyProvider,
  PolicyStatus 
} from '@/types/backend';

// Policy state interface
interface PolicyState {
  policies: PolicyPublic[];
  currentPolicy: PolicyPublic | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    policy_status?: PolicyStatus;
    policy_provider?: PolicyProvider;
    policy_type?: PolicyType;
  };
}

// Initial state
const initialState: PolicyState = {
  policies: [],
  currentPolicy: null,
  isLoading: false,
  error: null,
  filters: {},
};

// Async thunks
export const fetchPolicies = createAsyncThunk(
  'policy/fetchPolicies',
  async (filters?: {
    policy_status?: PolicyStatus;
    policy_provider?: PolicyProvider;
    policy_type?: PolicyType;
  }, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const policies = await apiService.policies.getUserPolicies(filters);
      return policies;
    } catch (error: any) {
      console.error('Fetch policies error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to fetch policies';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchPolicyById = createAsyncThunk(
  'policy/fetchPolicyById',
  async (policyId: string, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const policy = await apiService.policies.getPolicyById(policyId);
      return policy;
    } catch (error: any) {
      console.error('Fetch policy by ID error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to fetch policy';
      return rejectWithValue(errorMessage);
    }
  }
);

export const createPolicy = createAsyncThunk(
  'policy/createPolicy',
  async (policyData: PolicyCreate, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const policy = await apiService.policies.createPolicy(policyData);
      return policy;
    } catch (error: any) {
      console.error('Create policy error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to create policy';
      return rejectWithValue(errorMessage);
    }
  }
);

export const updatePolicy = createAsyncThunk(
  'policy/updatePolicy',
  async ({ policyId, policyData }: { policyId: string; policyData: PolicyUpdate }, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const policy = await apiService.policies.updatePolicy(policyId, policyData);
      return policy;
    } catch (error: any) {
      console.error('Update policy error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to update policy';
      return rejectWithValue(errorMessage);
    }
  }
);

// Policy slice
const policySlice = createSlice({
  name: 'policy',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearPolicies: (state) => {
      state.policies = [];
      state.currentPolicy = null;
    },
    setFilters: (state, action: PayloadAction<{
      policy_status?: PolicyStatus;
      policy_provider?: PolicyProvider;
      policy_type?: PolicyType;
    }>) => {
      state.filters = action.payload;
    },
    clearCurrentPolicy: (state) => {
      state.currentPolicy = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch policies
    builder.addCase(fetchPolicies.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchPolicies.fulfilled, (state, action) => {
      state.isLoading = false;
      state.policies = action.payload;
    });
    builder.addCase(fetchPolicies.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Fetch policy by ID
    builder.addCase(fetchPolicyById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchPolicyById.fulfilled, (state, action) => {
      state.isLoading = false;
      state.currentPolicy = action.payload;
    });
    builder.addCase(fetchPolicyById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Create policy
    builder.addCase(createPolicy.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(createPolicy.fulfilled, (state, action) => {
      state.isLoading = false;
      state.policies.push(action.payload);
      state.currentPolicy = action.payload;
    });
    builder.addCase(createPolicy.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Update policy
    builder.addCase(updatePolicy.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(updatePolicy.fulfilled, (state, action) => {
      state.isLoading = false;
      const index = state.policies.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.policies[index] = action.payload;
      }
      if (state.currentPolicy?.id === action.payload.id) {
        state.currentPolicy = action.payload;
      }
    });
    builder.addCase(updatePolicy.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
  },
});

export const { clearError, clearPolicies, setFilters, clearCurrentPolicy } = policySlice.actions;
export default policySlice.reducer;

// Selectors
export const selectPolicies = (state: { policy: PolicyState }) => state.policy.policies;
export const selectCurrentPolicy = (state: { policy: PolicyState }) => state.policy.currentPolicy;
export const selectIsPolicyLoading = (state: { policy: PolicyState }) => state.policy.isLoading;
export const selectPolicyError = (state: { policy: PolicyState }) => state.policy.error;
export const selectPolicyFilters = (state: { policy: PolicyState }) => state.policy.filters;

// Computed selectors
export const selectActivePolicies = (state: { policy: PolicyState }) => 
  state.policy.policies.filter(policy => policy.status === PolicyStatus.ACTIVE);

export const selectPoliciesByType = (policyType: PolicyType) => (state: { policy: PolicyState }) =>
  state.policy.policies.filter(policy => policy.policy_type === policyType);

export const selectPoliciesByProvider = (provider: PolicyProvider) => (state: { policy: PolicyState }) =>
  state.policy.policies.filter(policy => policy.policy_provider === provider);
