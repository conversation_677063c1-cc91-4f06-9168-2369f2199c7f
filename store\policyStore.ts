import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { InsuranceProductType } from '@/types/quote.types';

// Define policy types
export type PolicyStatus = 'active' | 'pending_renewal' | 'renewed' | 'cancelled' | 'expired';

export type PaymentFrequency = 'annual' | 'monthly' | 'quarterly' | 'semi_annual';

// Import the PolicyDocumentType from our types file
import { PolicyDocumentType } from '@/components/documents/types';

export type PolicyDocument = {
  id: string;
  policyId: string;
  type: PolicyDocumentType;
  name: string;
  description?: string;
  documentId: string;
  issueDate: string;
  expiryDate?: string;
  importance?: 'high' | 'medium' | 'low';
  relatedDocumentIds?: string[]; // IDs of related documents
  tags?: string[]; // For additional categorization
};

export type PolicyPayment = {
  id: string;
  policyId: string;
  amount: number;
  currency: string;
  dueDate: string;
  paidDate?: string;
  status: 'due' | 'paid' | 'overdue' | 'cancelled';
  paymentMethod?: string;
  reference?: string;
  receiptId?: string;
};

export type CoverageItem = {
  id: string;
  name: string;
  description?: string;
  coverAmount: number;
  premium: number;
  excess?: number;
  isAddOn: boolean;
};

export type Policy = {
  id: string;
  applicationId: string;
  quoteId: string;
  policyNumber: string;
  type: InsuranceProductType;
  status: PolicyStatus;
  startDate: string;
  endDate: string;
  renewalDate: string;
  premium: number;
  coverAmount: number;
  currency: string;
  paymentFrequency: PaymentFrequency;
  underwriter: string;
  clientInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address?: string;
    idNumber?: string;
  };
  documents: PolicyDocument[];
  payments: PolicyPayment[];
  coverage: CoverageItem[];
  notes?: string[];
  endorsements?: Array<{
    id: string;
    date: string;
    description: string;
    documentId?: string;
  }>;
  claims?: Array<{
    id: string;
    date: string;
    status: string;
    amount?: number;
    description: string;
  }>;
};

// Define policy state
interface PolicyState {
  policies: Policy[];
  currentPolicy: Policy | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchPolicies: () => Promise<void>;
  getPolicyById: (id: string) => Policy | undefined;
  getPolicyByNumber: (policyNumber: string) => Policy | undefined;
  createPolicyFromApplication: (applicationId: string, policyDetails: Partial<Policy>) => Promise<Policy>;
  updatePolicy: (id: string, updates: Partial<Policy>) => Promise<void>;
  updatePolicyStatus: (id: string, status: PolicyStatus) => Promise<void>;
  addPolicyDocument: (policyId: string, document: Omit<PolicyDocument, 'id'>) => Promise<void>;
  addPolicyPayment: (policyId: string, payment: Omit<PolicyPayment, 'id'>) => Promise<void>;
  updatePaymentStatus: (policyId: string, paymentId: string, status: PolicyPayment['status'], details?: any) => Promise<void>;
  initiateRenewal: (policyId: string) => Promise<void>;
  renewPolicy: (policyId: string) => Promise<void>;
  cancelPolicy: (policyId: string, reason: string) => Promise<void>;
}

// Helper function to load policies from storage
const loadPoliciesFromStorage = async (): Promise<Policy[] | null> => {
  try {
    const policiesJson = await AsyncStorage.getItem('policies');
    if (policiesJson) {
      return JSON.parse(policiesJson);
    }
    return null;
  } catch (error) {
    console.error('Error loading policies from storage:', error);
    return null;
  }
};

// Helper function to save policies to storage
const savePoliciestoStorage = async (policies: Policy[]): Promise<void> => {
  try {
    await AsyncStorage.setItem('policies', JSON.stringify(policies));
  } catch (error) {
    console.error('Error saving policies to storage:', error);
  }
};

// Create the store
const usePolicyStore = create<PolicyState>((set, get) => ({
  policies: [],
  currentPolicy: null,
  isLoading: false,
  error: null,

  // Fetch all policies
  fetchPolicies: async () => {
    set({ isLoading: true, error: null });
    try {
      // First, try to load policies from AsyncStorage
      const storedPolicies = await loadPoliciesFromStorage();

      // If we have stored policies, use them
      if (storedPolicies && storedPolicies.length > 0) {
        console.log('Using policies from AsyncStorage:', storedPolicies.length);
        set({ policies: storedPolicies, isLoading: false });
        return;
      }

      // If no stored policies, generate sample data for testing
      try {
        const { generateSamplePolicies } = await import('@/utils/simulateData');
        const samplePolicies = generateSamplePolicies(3);
        console.log('Generated sample policies:', samplePolicies.length);

        // Save to AsyncStorage
        await savePoliciestoStorage(samplePolicies);

        set({ policies: samplePolicies, isLoading: false });
        return;
      } catch (sampleError) {
        console.error('Error generating sample policies:', sampleError);
        // Continue with empty array if sample generation fails
      }

      // If no stored policies and sample generation fails, use empty array
      set({ policies: [], isLoading: false });
    } catch (error) {
      console.error('Error fetching policies:', error);
      set({
        error: 'Failed to fetch policies. Please try again.',
        isLoading: false
      });
    }
  },

  // Get a policy by ID
  getPolicyById: (id: string) => {
    return get().policies.find(policy => policy.id === id);
  },

  // Get a policy by policy number
  getPolicyByNumber: (policyNumber: string) => {
    return get().policies.find(policy => policy.policyNumber === policyNumber);
  },

  // Create a new policy from an application
  createPolicyFromApplication: async (applicationId: string, policyDetails: Partial<Policy>) => {
    set({ isLoading: true, error: null });
    try {
      // Generate ID and policy number if not provided
      const id = policyDetails.id || `policy-${Date.now()}`;
      const policyNumber = policyDetails.policyNumber || `POL-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`;

      // Set dates
      const startDate = policyDetails.startDate || new Date().toISOString().split('T')[0];

      // Calculate end date (1 year from start date by default)
      const endDateObj = new Date(startDate);
      endDateObj.setFullYear(endDateObj.getFullYear() + 1);
      const endDate = policyDetails.endDate || endDateObj.toISOString().split('T')[0];

      // Set renewal date (same as end date by default)
      const renewalDate = policyDetails.renewalDate || endDate;

      // Create the new policy
      const newPolicy: Policy = {
        id,
        applicationId,
        quoteId: policyDetails.quoteId || '',
        policyNumber,
        type: policyDetails.type || 'motor',
        status: policyDetails.status || 'active',
        startDate,
        endDate,
        renewalDate,
        premium: policyDetails.premium || 0,
        coverAmount: policyDetails.coverAmount || 0,
        currency: policyDetails.currency || 'P',
        paymentFrequency: policyDetails.paymentFrequency || 'annual',
        underwriter: policyDetails.underwriter || 'Inerca Insurance',
        clientInfo: policyDetails.clientInfo || {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
        },
        documents: policyDetails.documents || [],
        payments: policyDetails.payments || [],
        coverage: policyDetails.coverage || [],
        notes: policyDetails.notes || [],
        endorsements: policyDetails.endorsements || [],
        claims: policyDetails.claims || []
      };

      // Update state with new policy
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = [...state.policies, newPolicy];
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: newPolicy,
          isLoading: false
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast
      showToast(
        'success',
        'Policy Created',
        `Policy ${policyNumber} has been created successfully`,
        { visibilityTime: 3000 }
      );

      return newPolicy;
    } catch (error) {
      console.error('Error creating policy:', error);
      set({
        error: 'Failed to create policy. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update a policy
  updatePolicy: async (id: string, updates: Partial<Policy>) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === id);

      if (!policy) {
        throw new Error(`Policy with ID ${id} not found`);
      }

      // Update the policy
      const updatedPolicy = {
        ...policy,
        ...updates,
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === id ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === id ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      return;
    } catch (error) {
      console.error('Error updating policy:', error);
      set({
        error: 'Failed to update policy. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update policy status
  updatePolicyStatus: async (id: string, status: PolicyStatus) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === id);

      if (!policy) {
        throw new Error(`Policy with ID ${id} not found`);
      }

      // Update the policy
      const updatedPolicy = {
        ...policy,
        status,
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === id ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === id ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast
      showToast(
        'success',
        'Status Updated',
        `Policy status updated to ${status.replace('_', ' ')}`,
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error updating policy status:', error);
      set({
        error: 'Failed to update policy status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Add a policy document
  addPolicyDocument: async (policyId: string, document: Omit<PolicyDocument, 'id'>) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Policy with ID ${policyId} not found`);
      }

      // Create new document with ID
      const newDocument: PolicyDocument = {
        ...document,
        id: `doc-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      };

      // Update the policy
      const updatedPolicy = {
        ...policy,
        documents: [...policy.documents, newDocument],
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === policyId ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === policyId ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast
      showToast(
        'success',
        'Document Added',
        `${document.type.charAt(0).toUpperCase() + document.type.slice(1)} document has been added to your policy`,
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error adding policy document:', error);
      set({
        error: 'Failed to add policy document. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Add a policy payment
  addPolicyPayment: async (policyId: string, payment: Omit<PolicyPayment, 'id'>) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Policy with ID ${policyId} not found`);
      }

      // Create new payment with ID
      const newPayment: PolicyPayment = {
        ...payment,
        id: `payment-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      };

      // Update the policy
      const updatedPolicy = {
        ...policy,
        payments: [...policy.payments, newPayment],
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === policyId ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === policyId ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      return;
    } catch (error) {
      console.error('Error adding policy payment:', error);
      set({
        error: 'Failed to add policy payment. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update payment status
  updatePaymentStatus: async (policyId: string, paymentId: string, status: PolicyPayment['status'], details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Policy with ID ${policyId} not found`);
      }

      // Update the payment
      const updatedPayments = policy.payments.map(payment => {
        if (payment.id === paymentId) {
          return {
            ...payment,
            status,
            ...(status === 'paid' ? { paidDate: new Date().toISOString().split('T')[0] } : {}),
            ...(details?.reference ? { reference: details.reference } : {}),
            ...(details?.receiptId ? { receiptId: details.receiptId } : {}),
          };
        }
        return payment;
      });

      // Update the policy
      const updatedPolicy = {
        ...policy,
        payments: updatedPayments,
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === policyId ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === policyId ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast if payment is marked as paid
      if (status === 'paid') {
        showToast(
          'success',
          'Payment Recorded',
          'Your payment has been recorded successfully',
          { visibilityTime: 3000 }
        );
      }

      return;
    } catch (error) {
      console.error('Error updating payment status:', error);
      set({
        error: 'Failed to update payment status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Initiate policy renewal
  initiateRenewal: async (policyId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Policy with ID ${policyId} not found`);
      }

      // Update the policy status to pending renewal
      const updatedPolicy = {
        ...policy,
        status: 'pending_renewal' as PolicyStatus,
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === policyId ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === policyId ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast
      showToast(
        'success',
        'Renewal Initiated',
        'Your policy renewal has been initiated',
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error initiating renewal:', error);
      set({
        error: 'Failed to initiate renewal. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Renew policy
  renewPolicy: async (policyId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Policy with ID ${policyId} not found`);
      }

      // Calculate new dates
      const currentEndDate = new Date(policy.endDate);
      const newStartDate = new Date(currentEndDate);
      newStartDate.setDate(newStartDate.getDate() + 1);

      const newEndDate = new Date(newStartDate);
      newEndDate.setFullYear(newEndDate.getFullYear() + 1);

      const newRenewalDate = new Date(newEndDate);
      newRenewalDate.setDate(newRenewalDate.getDate() - 30);

      // Create renewal document
      const renewalDocument: PolicyDocument = {
        id: `doc-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        policyId: policy.id,
        type: 'renewal_notice',
        name: 'Policy Renewal Notice',
        description: `Renewal notice for policy ${policy.policyNumber}`,
        documentId: `renewal-${Date.now()}`,
        issueDate: new Date().toISOString(),
        importance: 'high',
      };

      // Create renewal payment
      const renewalPayment: PolicyPayment = {
        id: `payment-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        policyId: policy.id,
        amount: policy.premium,
        currency: policy.currency,
        dueDate: newStartDate.toISOString(),
        status: 'due',
      };

      // Update the policy
      const updatedPolicy = {
        ...policy,
        status: 'active' as PolicyStatus,
        startDate: newStartDate.toISOString(),
        endDate: newEndDate.toISOString(),
        renewalDate: newRenewalDate.toISOString(),
        documents: [...policy.documents, renewalDocument],
        payments: [...policy.payments, renewalPayment],
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === policyId ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === policyId ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast
      showToast(
        'success',
        'Policy Renewed',
        'Your policy has been successfully renewed',
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error renewing policy:', error);
      set({
        error: 'Failed to renew policy. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Cancel policy
  cancelPolicy: async (policyId: string, reason: string) => {
    set({ isLoading: true, error: null });
    try {
      // Find the policy
      const policy = get().policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Policy with ID ${policyId} not found`);
      }

      // Update the policy
      const updatedPolicy = {
        ...policy,
        status: 'cancelled' as PolicyStatus,
        notes: [...(policy.notes || []), `Cancelled on ${new Date().toISOString().split('T')[0]} - Reason: ${reason}`],
      };

      // Update state
      let updatedPolicies: Policy[] = [];
      set(state => {
        const newPolicies = state.policies.map(p =>
          p.id === policyId ? updatedPolicy : p
        );
        updatedPolicies = newPolicies;
        return {
          policies: newPolicies,
          currentPolicy: state.currentPolicy?.id === policyId ? updatedPolicy : state.currentPolicy,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await savePoliciestoStorage(updatedPolicies);

      // Show success toast
      showToast(
        'info',
        'Policy Cancelled',
        'Your policy has been cancelled',
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error cancelling policy:', error);
      set({
        error: 'Failed to cancel policy. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
}));

export default usePolicyStore;
