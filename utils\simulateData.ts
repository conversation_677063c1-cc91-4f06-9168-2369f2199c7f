import { Application } from '@/store/applicationStore';
import { Policy, PolicyDocument, PolicyPayment, CoverageItem } from '@/store/policyStore';
import { Quote } from '@/types/quote.types';

/**
 * Generate a sample policy for testing
 */
export const generateSamplePolicy = (index: number = 0): Policy => {
  const policyTypes = ['Motor', 'Houseowners', 'Household Contents', 'All Risks', 'Life'];
  const policyStatuses = ['active', 'pending_renewal', 'renewed', 'cancelled', 'expired'] as const;
  const paymentFrequencies = ['annual', 'monthly', 'quarterly', 'semi_annual'] as const;
  
  // Generate dates
  const today = new Date();
  const startDate = new Date();
  startDate.setMonth(today.getMonth() - 6 - index);
  
  const endDate = new Date(startDate);
  endDate.setFullYear(endDate.getFullYear() + 1);
  
  const renewalDate = new Date(endDate);
  
  // Generate policy ID and number
  const id = `policy-${Date.now()}-${index}`;
  const policyNumber = `POL-${startDate.getFullYear()}-${String(10000 + index).padStart(5, '0')}`;
  
  // Select policy type and status
  const type = policyTypes[index % policyTypes.length].toLowerCase() as any;
  const status = policyStatuses[index % policyStatuses.length];
  
  // Generate premium and coverage amount
  const premium = 1000 + (index * 500);
  const coverAmount = premium * 100;
  
  // Generate client info
  const clientInfo = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+267 71234567',
    address: '123 Main Street, Gaborone',
    idNumber: 'ID12345678',
  };
  
  // Generate documents
  const documents: PolicyDocument[] = [
    {
      id: `doc-${Date.now()}-1-${index}`,
      policyId: id,
      type: 'policy',
      name: `${type.charAt(0).toUpperCase() + type.slice(1)} Insurance Policy`,
      description: 'Official policy document',
      documentId: `document-${Date.now()}-1-${index}`,
      issueDate: startDate.toISOString().split('T')[0],
    },
    {
      id: `doc-${Date.now()}-2-${index}`,
      policyId: id,
      type: 'schedule',
      name: 'Policy Schedule',
      description: 'Details of coverage and premiums',
      documentId: `document-${Date.now()}-2-${index}`,
      issueDate: startDate.toISOString().split('T')[0],
    },
  ];
  
  // Generate payments
  const payments: PolicyPayment[] = [
    {
      id: `payment-${Date.now()}-1-${index}`,
      policyId: id,
      amount: premium,
      currency: 'P',
      dueDate: startDate.toISOString().split('T')[0],
      paidDate: startDate.toISOString().split('T')[0],
      status: 'paid',
      paymentMethod: 'credit_card',
      reference: `REF-${Date.now()}-1-${index}`,
    },
  ];
  
  // If policy is not new, add more payment history
  if (index > 0) {
    const secondPaymentDate = new Date(startDate);
    secondPaymentDate.setMonth(secondPaymentDate.getMonth() + 3);
    
    payments.push({
      id: `payment-${Date.now()}-2-${index}`,
      policyId: id,
      amount: premium / 4,
      currency: 'P',
      dueDate: secondPaymentDate.toISOString().split('T')[0],
      paidDate: secondPaymentDate.toISOString().split('T')[0],
      status: 'paid',
      paymentMethod: 'bank_transfer',
      reference: `REF-${Date.now()}-2-${index}`,
    });
    
    // Add an upcoming payment
    const upcomingPaymentDate = new Date(today);
    upcomingPaymentDate.setDate(upcomingPaymentDate.getDate() + 14);
    
    payments.push({
      id: `payment-${Date.now()}-3-${index}`,
      policyId: id,
      amount: premium / 4,
      currency: 'P',
      dueDate: upcomingPaymentDate.toISOString().split('T')[0],
      status: 'due',
      reference: `REF-${Date.now()}-3-${index}`,
    });
  }
  
  // Generate coverage items
  const coverage: CoverageItem[] = [];
  
  // Add main coverage
  coverage.push({
    id: `coverage-${Date.now()}-1-${index}`,
    name: `${type.charAt(0).toUpperCase() + type.slice(1)} Base Coverage`,
    description: `Comprehensive coverage for your ${type}`,
    coverAmount: coverAmount * 0.8,
    premium: premium * 0.8,
    excess: premium * 0.05,
    isAddOn: false,
  });
  
  // Add add-ons
  coverage.push({
    id: `coverage-${Date.now()}-2-${index}`,
    name: 'Extended Protection',
    description: 'Additional protection for special circumstances',
    coverAmount: coverAmount * 0.1,
    premium: premium * 0.1,
    isAddOn: true,
  });
  
  coverage.push({
    id: `coverage-${Date.now()}-3-${index}`,
    name: 'Premium Support',
    description: 'Enhanced customer support and claim handling',
    coverAmount: coverAmount * 0.1,
    premium: premium * 0.1,
    isAddOn: true,
  });
  
  // Create the policy
  return {
    id,
    applicationId: `app-${Date.now()}-${index}`,
    quoteId: `quote-${Date.now()}-${index}`,
    policyNumber,
    type,
    status,
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
    renewalDate: renewalDate.toISOString().split('T')[0],
    premium,
    coverAmount,
    currency: 'P',
    paymentFrequency: paymentFrequencies[index % paymentFrequencies.length],
    underwriter: 'Inerca Insurance',
    clientInfo,
    documents,
    payments,
    coverage,
    notes: [
      `Policy created on ${startDate.toISOString().split('T')[0]}`,
    ],
    endorsements: [],
    claims: [],
  };
};

/**
 * Generate multiple sample policies
 */
export const generateSamplePolicies = (count: number = 5): Policy[] => {
  return Array.from({ length: count }, (_, i) => generateSamplePolicy(i));
};

/**
 * Generate a sample application in a specific status
 */
export const generateSampleApplication = (
  status: 'quote_accepted' | 'submitted' | 'payment_pending' | 'payment_verified' | 'underwriting' | 'approved' | 'policy_issued',
  index: number = 0
): Application => {
  // Implementation will be added later
  return {} as Application;
};

/**
 * Generate multiple sample applications in various stages
 */
export const generateSampleApplications = (count: number = 5): Application[] => {
  const statuses = [
    'quote_accepted',
    'payment_pending',
    'payment_verified',
    'underwriting',
    'approved',
    'policy_issued'
  ] as const;
  
  return Array.from({ length: count }, (_, i) => 
    generateSampleApplication(statuses[i % statuses.length], i)
  );
};
